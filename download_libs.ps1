$libsDir = "app/libs"

# Create the directory if it doesn't exist
if (-not (Test-Path $libsDir)) {
    New-Item -ItemType Directory -Path $libsDir -Force
}

# URLs for the native libraries
$urls = @{
    "gdx-natives.jar" = "https://repo1.maven.org/maven2/com/badlogicgames/gdx/gdx-platform/1.12.0/gdx-platform-1.12.0-natives-desktop.jar"
    "gdx-box2d-natives.jar" = "https://repo1.maven.org/maven2/com/badlogicgames/gdx/gdx-box2d-platform/1.12.0/gdx-box2d-platform-1.12.0-natives-desktop.jar"
    "gdx-freetype-natives.jar" = "https://repo1.maven.org/maven2/com/badlogicgames/gdx/gdx-freetype-platform/1.12.0/gdx-freetype-platform-1.12.0-natives-desktop.jar"
}

# Download each library
foreach ($lib in $urls.Keys) {
    $url = $urls[$lib]
    $outputPath = Join-Path $libsDir $lib
    
    Write-Host "Downloading $lib from $url"
    Invoke-WebRequest -Uri $url -OutFile $outputPath
    
    if (Test-Path $outputPath) {
        Write-Host "Successfully downloaded $lib to $outputPath"
    } else {
        Write-Host "Failed to download $lib"
    }
}

Write-Host "All libraries downloaded successfully!"
