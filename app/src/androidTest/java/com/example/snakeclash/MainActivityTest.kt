package com.example.snakeclash

import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * Instrumented test for MainActivity.
 * Tests the activity lifecycle and basic functionality.
 */
@RunWith(AndroidJUnit4::class)
@LargeTest
class MainActivityTest {
    
    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)
    
    @Test
    fun testActivityLaunch() {
        // Simply launching the activity without crashing is a success
        activityRule.scenario.onActivity { activity ->
            // Activity is created and running
            assert(activity.isDestroyed.not())
        }
    }
    
    @Test
    fun testActivityLifecycle() {
        // Test activity lifecycle
        activityRule.scenario.onActivity { activity ->
            // Activity is created
            assert(activity.isDestroyed.not())
        }
        
        // Recreate activity
        activityRule.scenario.recreate()
        
        activityRule.scenario.onActivity { activity ->
            // Activity is recreated
            assert(activity.isDestroyed.not())
        }
    }
}
