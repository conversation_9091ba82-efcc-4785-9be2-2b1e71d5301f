package com.example.snakeclash

import android.view.KeyEvent
import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.pressKey
import androidx.test.espresso.matcher.ViewMatchers.isDisplayed
import androidx.test.espresso.matcher.ViewMatchers.withId
import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * System test for the entire Snake Clash game.
 * Tests the game as a whole from the user's perspective.
 */
@RunWith(AndroidJUnit4::class)
@LargeTest
class GameSystemTest {
    
    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)
    
    @Test
    fun testGameLaunchAndPlay() {
        // Wait for game to initialize
        Thread.sleep(1000)
        
        // Simulate playing the game for a few seconds
        for (i in 1..10) {
            // Press different direction keys
            when (i % 4) {
                0 -> onView(isDisplayed()).perform(pressKey(KeyEvent.KEYCODE_DPAD_UP))
                1 -> onView(isDisplayed()).perform(pressKey(KeyEvent.KEYCODE_DPAD_RIGHT))
                2 -> onView(isDisplayed()).perform(pressKey(KeyEvent.KEYCODE_DPAD_DOWN))
                3 -> onView(isDisplayed()).perform(pressKey(KeyEvent.KEYCODE_DPAD_LEFT))
            }
            
            // Wait a bit between key presses
            Thread.sleep(300)
        }
        
        // Game should still be running without crashes
        activityRule.scenario.onActivity { activity ->
            assert(activity.isDestroyed.not())
        }
    }
    
    @Test
    fun testGamePauseAndResume() {
        // Wait for game to initialize
        Thread.sleep(1000)
        
        // Pause the activity
        activityRule.scenario.onActivity { activity ->
            activity.onPause()
        }
        
        // Wait a bit
        Thread.sleep(500)
        
        // Resume the activity
        activityRule.scenario.onActivity { activity ->
            activity.onResume()
        }
        
        // Wait a bit more
        Thread.sleep(500)
        
        // Game should still be running without crashes
        activityRule.scenario.onActivity { activity ->
            assert(activity.isDestroyed.not())
        }
    }
    
    @Test
    fun testGameSurvivesConfigurationChange() {
        // Wait for game to initialize
        Thread.sleep(1000)
        
        // Recreate the activity (simulates configuration change)
        activityRule.scenario.recreate()
        
        // Wait for recreation
        Thread.sleep(1000)
        
        // Game should still be running without crashes
        activityRule.scenario.onActivity { activity ->
            assert(activity.isDestroyed.not())
        }
    }
}
