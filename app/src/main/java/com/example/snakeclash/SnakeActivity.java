package com.example.snakeclash;

import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.example.snakeclash.game.GameEngine;
import com.example.snakeclash.game.SnakeGameView;

/**
 * Android launcher for the Snake Clash game.
 */
public class SnakeActivity extends AppCompatActivity implements GameEngine.GameCallbacks, SnakeGameView.GameCallbacks {
    // Game view
    private SnakeGameView gameView;

    // UI elements
    private TextView scoreTextView;
    private View gameOverView;
    private Button restartButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Get UI elements
        scoreTextView = findViewById(R.id.score_text);
        gameOverView = findViewById(R.id.game_over_layout);
        restartButton = findViewById(R.id.restart_button);
        FrameLayout gameContainer = findViewById(R.id.game_container);

        // Set up restart button
        restartButton.setOnClickListener(v -> {
            gameOverView.setVisibility(View.GONE);
            if (gameView != null) {
                gameView.restart();
            }
        });

        // Create the game view
        gameView = new SnakeGameView(this);
        gameView.setCallbacks(this);

        // Add the game view to the container
        gameContainer.addView(gameView);

        // Hide the score TextView since we're drawing it directly in the game view
        scoreTextView.setVisibility(View.GONE);
    }

    @Override
    public void onConfigurationChanged(android.content.res.Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // The game view will automatically adjust to the new orientation
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (gameView != null) {
            gameView.pause();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (gameView != null) {
            gameView.resume();
        }
    }

    @Override
    public void onScoreChanged(int playerId, int score) {
        // Update score UI
        runOnUiThread(() -> scoreTextView.setText("Score: " + score));
    }

    @Override
    public void onGameOver(int winnerId) {
        // Show game over UI
        runOnUiThread(() -> gameOverView.setVisibility(View.VISIBLE));
    }

    @Override
    public void onPausePressed() {
        // Show pause dialog
        runOnUiThread(() -> {
            // Pause the game
            if (gameView != null) {
                gameView.pause();
            }

            // Create and show a pause dialog
            android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
            builder.setTitle("Game Paused");
            builder.setMessage("What would you like to do?");

            // Resume button
            builder.setPositiveButton("Resume", (dialog, which) -> {
                if (gameView != null) {
                    gameView.resume();
                }
                dialog.dismiss();
            });

            // Restart button
            builder.setNegativeButton("Restart", (dialog, which) -> {
                if (gameView != null) {
                    gameView.restart();
                    gameView.resume();
                }
                dialog.dismiss();
            });

            // Create and show the dialog
            android.app.AlertDialog dialog = builder.create();
            dialog.setCanceledOnTouchOutside(false);
            dialog.setOnCancelListener(dialogInterface -> {
                if (gameView != null) {
                    gameView.resume();
                }
            });
            dialog.show();
        });
    }

    @Override
    public void onPowerUpSpawned(String message) {
        // Show a toast message when a power-up appears
        runOnUiThread(() -> {
            android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show();

            // Also show the notification in the game view
            if (gameView != null) {
                gameView.showPowerUpNotification(message);
            }
        });
    }

    @Override
    public boolean dispatchKeyEvent(android.view.KeyEvent event) {
        // Forward key events to the game view
        if (event.getAction() == android.view.KeyEvent.ACTION_DOWN && gameView != null) {
            if (gameView.onKeyDown(event.getKeyCode(), event)) {
                return true;
            }
        }
        return super.dispatchKeyEvent(event);
    }
}
