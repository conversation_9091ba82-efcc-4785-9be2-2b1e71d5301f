package com.example.snakeclash.game;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;

/**
 * Represents a food item.
 */
public class Food {
    private int x, y;
    private int color;
    private float pulseTime;
    private float pulseScale;
    private float spawnTime;
    private boolean isSpawning;
    private static final float SPAWN_DURATION = 0.5f; // seconds

    public Food(int x, int y, int color) {
        this.x = x;
        this.y = y;
        this.color = color;
        this.pulseTime = 0;
        this.pulseScale = 0.0f; // Start small for spawn animation
        this.spawnTime = 0;
        this.isSpawning = true;
    }

    public void update(float deltaTime) {
        if (isSpawning) {
            // Handle spawn animation
            spawnTime += deltaTime;
            if (spawnTime >= SPAWN_DURATION) {
                isSpawning = false;
                pulseScale = 1.0f;
            } else {
                // Grow from 0 to 1 during spawn
                pulseScale = spawnTime / SPAWN_DURATION;
            }
        } else {
            // Normal pulsing animation
            pulseTime += deltaTime * 2;
            pulseScale = 0.8f + 0.2f * (float) Math.sin(pulseTime);
        }
    }

    public void render(Canvas canvas, Paint paint, float cellSize, float offsetX, float offsetY) {
        float radius = cellSize * 0.4f * pulseScale;
        float centerX = offsetX + x * cellSize + cellSize / 2;
        float centerY = offsetY + y * cellSize + cellSize / 2;

        // Use enhanced food rendering
        GraphicsUtils.drawEnhancedFood(canvas, paint, centerX, centerY, radius, color, pulseScale, isSpawning);
    }

    public int getX() {
        return x;
    }

    public int getY() {
        return y;
    }

    public int getColor() {
        return color;
    }
}
