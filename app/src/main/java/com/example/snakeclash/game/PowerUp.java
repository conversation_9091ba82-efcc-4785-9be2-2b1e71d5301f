package com.example.snakeclash.game;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;

import java.util.Random;

/**
 * Represents a power-up in the game.
 */
public class PowerUp {
    private int x, y;
    private PowerUpType type;
    private float pulseTime;
    private float pulseScale;
    private float spawnTime;
    private boolean isSpawning;
    private boolean isActive;
    private float activeTime;
    public static final float SPAWN_DURATION = 0.5f; // seconds
    public static final float ACTIVE_DURATION = 10.0f; // seconds for speed boost
    private static final float PULSE_SPEED = 3.0f; // faster pulse than food

    public PowerUp(int x, int y, PowerUpType type) {
        this.x = x;
        this.y = y;
        this.type = type;
        this.pulseTime = 0;
        this.pulseScale = 0.0f; // Start small for spawn animation
        this.spawnTime = 0;
        this.isSpawning = true;
        this.isActive = false;
        this.activeTime = 0;
    }

    public void update(float deltaTime) {
        if (isActive) {
            // Update active time
            activeTime += deltaTime;
            if (activeTime >= ACTIVE_DURATION) {
                isActive = false;
            }
            return;
        }

        if (isSpawning) {
            // Handle spawn animation
            spawnTime += deltaTime;
            if (spawnTime >= SPAWN_DURATION) {
                isSpawning = false;
                pulseScale = 1.0f;
            } else {
                // Grow from 0 to 1 during spawn
                pulseScale = spawnTime / SPAWN_DURATION;
            }
        } else {
            // Normal pulsing animation (faster than food)
            pulseTime += deltaTime * PULSE_SPEED;
            pulseScale = 0.7f + 0.3f * (float) Math.sin(pulseTime);
        }
    }

    public void render(Canvas canvas, Paint paint, float cellSize, float offsetX, float offsetY) {
        float radius = cellSize * 0.4f * pulseScale;
        float centerX = offsetX + x * cellSize + cellSize / 2;
        float centerY = offsetY + y * cellSize + cellSize / 2;

        // Use enhanced power-up rendering
        GraphicsUtils.drawEnhancedPowerUp(canvas, paint, centerX, centerY, radius, type, pulseScale, isSpawning);
    }

    public void activate() {
        isActive = true;
        activeTime = 0;
    }

    public boolean isExpired() {
        return isActive && activeTime >= ACTIVE_DURATION;
    }

    public int getX() {
        return x;
    }

    public int getY() {
        return y;
    }

    public PowerUpType getType() {
        return type;
    }

    public boolean isActive() {
        return isActive;
    }

    public float getActiveTime() {
        return activeTime;
    }
}
