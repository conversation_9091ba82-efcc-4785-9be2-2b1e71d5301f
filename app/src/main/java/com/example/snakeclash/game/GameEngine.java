package com.example.snakeclash.game;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Handles the core game logic and state management.
 */
public class GameEngine {
    // Game objects
    private List<Snake> snakes;
    private List<Food> foods;
    private List<Obstacle> obstacles;
    private List<PowerUp> powerUps;
    
    // Game settings
    public static final int GRID_SIZE = 20;
    public static final int MAX_SNAKES = 6;
    public static final int MAX_FOODS = 20;
    public static final int MAX_OBSTACLES = 15;
    public static final float FOOD_SPAWN_INTERVAL = 2f; // seconds
    public static final float POWERUP_SPAWN_INTERVAL = 60f; // 1 minute
    public static final float INITIAL_POWERUP_DELAY = 15f; // 15 seconds before first power-up
    
    // Game state
    private boolean isGameOver;
    private int score;
    private float foodSpawnTimer;
    private float powerUpSpawnTimer;
    
    // Callbacks
    private GameCallbacks callbacks;
    
    /**
     * Constructor for the game engine.
     */
    public GameEngine() {
        initGame();
    }
    
    /**
     * Initializes the game.
     */
    public void initGame() {
        // Initialize game state
        isGameOver = false;
        score = 0;
        foodSpawnTimer = 0;
        powerUpSpawnTimer = POWERUP_SPAWN_INTERVAL - INITIAL_POWERUP_DELAY; // Start with initial delay
        
        // Initialize game objects
        snakes = new ArrayList<>();
        foods = new ArrayList<>();
        obstacles = new ArrayList<>();
        powerUps = new ArrayList<>();
        
        // Get effective grid size
        int[] gridSizes = getEffectiveGridSize();
        int effectiveGridWidth = gridSizes[0];
        int effectiveGridHeight = gridSizes[1];
        
        // Create player snake
        Snake playerSnake = new Snake(0, effectiveGridWidth / 2, effectiveGridHeight / 2, 0xFF00FF00, false);
        snakes.add(playerSnake);
        
        // Create AI snakes
        for (int i = 1; i < MAX_SNAKES; i++) {
            int x = new Random().nextInt(effectiveGridWidth);
            int y = new Random().nextInt(effectiveGridHeight);
            
            // Generate random color
            int r = 100 + new Random().nextInt(155);
            int g = 100 + new Random().nextInt(155);
            int b = 100 + new Random().nextInt(155);
            int color = (0xFF << 24) | (r << 16) | (g << 8) | b;
            
            Snake aiSnake = new Snake(i, x, y, color, true);
            snakes.add(aiSnake);
        }
        
        // Create obstacles
        createObstacles(effectiveGridWidth, effectiveGridHeight);
        
        // Create initial food
        for (int i = 0; i < MAX_FOODS / 2; i++) {
            spawnFood();
        }
    }
    
    /**
     * Updates the game state.
     */
    public void update(float deltaTime) {
        if (isGameOver) return;
        
        // Update food spawn timer
        foodSpawnTimer += deltaTime;
        if (foodSpawnTimer >= FOOD_SPAWN_INTERVAL) {
            foodSpawnTimer = 0;
            spawnFood();
        }
        
        // Update power-up spawn timer
        powerUpSpawnTimer += deltaTime;
        if (powerUpSpawnTimer >= POWERUP_SPAWN_INTERVAL) {
            powerUpSpawnTimer = 0;
            spawnPowerUp();
        }
        
        // Update food animations
        for (Food food : foods) {
            food.update(deltaTime);
        }
        
        // Update power-ups
        for (int i = powerUps.size() - 1; i >= 0; i--) {
            PowerUp powerUp = powerUps.get(i);
            powerUp.update(deltaTime);
            
            // Remove expired active power-ups
            if (powerUp.isExpired()) {
                // Deactivate the power-up effect
                if (powerUp.getType() == PowerUpType.SPEED) {
                    // Reset speed for all snakes
                    for (Snake snake : snakes) {
                        snake.resetSpeed();
                    }
                }
                
                powerUps.remove(i);
            }
        }
        
        // Update snakes
        for (Snake snake : snakes) {
            snake.update(deltaTime, foods, snakes, obstacles);
            
            // Check for power-up collision
            checkPowerUpCollision(snake);
            
            // Update score for player
            if (!snake.isAI() && snake == snakes.get(0)) {
                score = snake.getSize();
                if (callbacks != null) {
                    callbacks.onScoreChanged(0, score);
                }
            }
        }
        
        // Check for game over condition
        checkGameOver();
    }
    
    /**
     * Checks if a snake has collided with a power-up.
     */
    private void checkPowerUpCollision(Snake snake) {
        if (snake.getBody().isEmpty()) return;
        
        Point head = snake.getHead();
        
        for (int i = powerUps.size() - 1; i >= 0; i--) {
            PowerUp powerUp = powerUps.get(i);
            
            // Skip active power-ups
            if (powerUp.isActive()) continue;
            
            // Check collision
            if (head.x == powerUp.getX() && head.y == powerUp.getY()) {
                // Apply power-up effect
                if (powerUp.getType() == PowerUpType.SPEED) {
                    // Double the snake's speed
                    snake.doubleSpeed();
                }
                
                // Activate the power-up
                powerUp.activate();
                
                // Show visual feedback
                snake.setSpeedBoost(true);
                
                break;
            }
        }
    }
    
    /**
     * Checks if the game is over.
     */
    private void checkGameOver() {
        // Game is over if player is dead or has only head left
        Snake playerSnake = snakes.get(0);
        if (playerSnake.getSize() <= 1) {
            isGameOver = true;
            if (callbacks != null) {
                callbacks.onGameOver(-1);
            }
        }
    }
    
    /**
     * Creates random obstacles in the game world.
     */
    private void createObstacles(int gridWidth, int gridHeight) {
        Random random = new Random();
        
        // Determine number of obstacle patterns to create
        int numPatterns = 2 + random.nextInt(3); // 2-4 patterns
        
        for (int pattern = 0; pattern < numPatterns; pattern++) {
            // Choose a random pattern type
            int patternType = random.nextInt(4);
            
            // Choose a random position for the pattern center
            int centerX = gridWidth / 4 + random.nextInt(gridWidth / 2);
            int centerY = gridHeight / 4 + random.nextInt(gridHeight / 2);
            
            // Create the pattern
            switch (patternType) {
                case 0: // Horizontal wall
                    int wallLength = 3 + random.nextInt(5); // 3-7 blocks
                    int wallX = centerX - wallLength / 2;
                    for (int i = 0; i < wallLength; i++) {
                        if (isValidObstaclePosition(wallX + i, centerY, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(wallX + i, centerY, ObstacleType.BLOCK));
                        }
                    }
                    break;
                    
                case 1: // Vertical wall
                    wallLength = 3 + random.nextInt(5); // 3-7 blocks
                    int wallY = centerY - wallLength / 2;
                    for (int i = 0; i < wallLength; i++) {
                        if (isValidObstaclePosition(centerX, wallY + i, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX, wallY + i, ObstacleType.BLOCK));
                        }
                    }
                    break;
                    
                case 2: // L-shaped wall
                    int hLength = 3 + random.nextInt(3); // 3-5 blocks
                    int vLength = 3 + random.nextInt(3); // 3-5 blocks
                    for (int i = 0; i < hLength; i++) {
                        if (isValidObstaclePosition(centerX + i, centerY, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX + i, centerY, ObstacleType.BLOCK));
                        }
                    }
                    for (int i = 0; i < vLength; i++) {
                        if (isValidObstaclePosition(centerX, centerY + i, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX, centerY + i, ObstacleType.BLOCK));
                        }
                    }
                    break;
                    
                case 3: // Small room (square with opening)
                    int size = 3 + random.nextInt(2); // 3-4 blocks per side
                    int openingPos = random.nextInt(4); // 0=top, 1=right, 2=bottom, 3=left
                    int openingOffset = random.nextInt(size - 1); // Position of the opening
                    
                    // Top wall
                    for (int i = 0; i < size; i++) {
                        if (openingPos == 0 && i == openingOffset) continue; // Skip for opening
                        if (isValidObstaclePosition(centerX - size/2 + i, centerY - size/2, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX - size/2 + i, centerY - size/2, ObstacleType.BLOCK));
                        }
                    }
                    
                    // Right wall
                    for (int i = 0; i < size; i++) {
                        if (openingPos == 1 && i == openingOffset) continue; // Skip for opening
                        if (isValidObstaclePosition(centerX + size/2, centerY - size/2 + i, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX + size/2, centerY - size/2 + i, ObstacleType.BLOCK));
                        }
                    }
                    
                    // Bottom wall
                    for (int i = 0; i < size; i++) {
                        if (openingPos == 2 && i == openingOffset) continue; // Skip for opening
                        if (isValidObstaclePosition(centerX - size/2 + i, centerY + size/2, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX - size/2 + i, centerY + size/2, ObstacleType.BLOCK));
                        }
                    }
                    
                    // Left wall
                    for (int i = 0; i < size; i++) {
                        if (openingPos == 3 && i == openingOffset) continue; // Skip for opening
                        if (isValidObstaclePosition(centerX - size/2, centerY - size/2 + i, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX - size/2, centerY - size/2 + i, ObstacleType.BLOCK));
                        }
                    }
                    break;
            }
        }
        
        // Ensure we don't exceed the maximum number of obstacles
        while (obstacles.size() > MAX_OBSTACLES) {
            obstacles.remove(obstacles.size() - 1);
        }
    }
    
    /**
     * Checks if a position is valid for placing an obstacle.
     */
    private boolean isValidObstaclePosition(int x, int y, int gridWidth, int gridHeight) {
        // Check grid boundaries
        if (x < 0 || x >= gridWidth || y < 0 || y >= gridHeight) {
            return false;
        }
        
        // Check if position is already occupied by a snake
        for (Snake snake : snakes) {
            for (Point segment : snake.getBody()) {
                // Keep some distance from snake starting positions
                if (Math.abs(segment.x - x) < 3 && Math.abs(segment.y - y) < 3) {
                    return false;
                }
            }
        }
        
        // Check if position is already occupied by another obstacle
        for (Obstacle obstacle : obstacles) {
            if (obstacle.getX() == x && obstacle.getY() == y) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Spawns a new food item.
     */
    private void spawnFood() {
        if (foods.size() >= MAX_FOODS) return;
        
        // Get effective grid size
        int[] gridSizes = getEffectiveGridSize();
        int effectiveGridWidth = gridSizes[0];
        int effectiveGridHeight = gridSizes[1];
        
        // Try to find a valid position (max 10 attempts)
        for (int attempt = 0; attempt < 10; attempt++) {
            // Generate random position
            int x = new Random().nextInt(effectiveGridWidth);
            int y = new Random().nextInt(effectiveGridHeight);
            
            // Check if position is valid
            if (isValidFoodPosition(x, y)) {
                // Create new food
                int r = 100 + new Random().nextInt(155);
                int g = 100 + new Random().nextInt(155);
                int b = 100 + new Random().nextInt(155);
                int color = (0xFF << 24) | (r << 16) | (g << 8) | b;
                
                Food food = new Food(x, y, color);
                foods.add(food);
                return;
            }
        }
    }
    
    /**
     * Spawns a new power-up at a random location.
     */
    private void spawnPowerUp() {
        // Only one speed power-up at a time
        for (PowerUp powerUp : powerUps) {
            if (powerUp.getType() == PowerUpType.SPEED && !powerUp.isActive()) {
                return; // Already have an inactive speed power-up
            }
        }
        
        // Get effective grid size
        int[] gridSizes = getEffectiveGridSize();
        int effectiveGridWidth = gridSizes[0];
        int effectiveGridHeight = gridSizes[1];
        
        // Try to find a valid position (max 20 attempts to ensure randomness)
        Random random = new Random();
        for (int attempt = 0; attempt < 20; attempt++) {
            // Generate random position
            // Avoid edges by using a smaller range
            int x = 2 + random.nextInt(effectiveGridWidth - 4);
            int y = 2 + random.nextInt(effectiveGridHeight - 4);
            
            // Check if position is valid
            if (isValidFoodPosition(x, y)) {
                // Create new power-up
                PowerUp powerUp = new PowerUp(x, y, PowerUpType.SPEED);
                powerUps.add(powerUp);
                
                // Show notification about power-up spawn
                String message = "Speed power-up has appeared!";
                
                // Also notify through callback
                if (callbacks != null) {
                    callbacks.onPowerUpSpawned(message);
                }
                
                return;
            }
        }
    }
    
    /**
     * Checks if a position is valid for placing food or power-ups.
     */
    private boolean isValidFoodPosition(int x, int y) {
        // Check if position is occupied by a snake
        for (Snake snake : snakes) {
            for (Point point : snake.getBody()) {
                if (point.x == x && point.y == y) {
                    return false;
                }
            }
        }
        
        // Check if position is occupied by existing food
        for (Food food : foods) {
            if (food.getX() == x && food.getY() == y) {
                return false;
            }
        }
        
        // Check if position is occupied by a power-up
        for (PowerUp powerUp : powerUps) {
            if (powerUp.getX() == x && powerUp.getY() == y) {
                return false;
            }
        }
        
        // Check if position is occupied by an obstacle
        for (Obstacle obstacle : obstacles) {
            if (obstacle.getX() == x && obstacle.getY() == y) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Gets the effective grid size based on the current orientation.
     * @return An array with [effectiveGridWidth, effectiveGridHeight]
     */
    public int[] getEffectiveGridSize() {
        boolean isPortrait = true; // Default to portrait
        
        // In a real implementation, this would check the actual screen dimensions
        // For now, we'll just use a fixed value
        
        int effectiveGridWidth = isPortrait ? GRID_SIZE : (int)(GRID_SIZE * 1.5);
        int effectiveGridHeight = isPortrait ? (int)(GRID_SIZE * 1.5) : GRID_SIZE;
        return new int[] { effectiveGridWidth, effectiveGridHeight };
    }
    
    /**
     * Sets the game callbacks.
     */
    public void setCallbacks(GameCallbacks callbacks) {
        this.callbacks = callbacks;
    }
    
    /**
     * Gets the snakes.
     */
    public List<Snake> getSnakes() {
        return snakes;
    }
    
    /**
     * Gets the foods.
     */
    public List<Food> getFoods() {
        return foods;
    }
    
    /**
     * Gets the obstacles.
     */
    public List<Obstacle> getObstacles() {
        return obstacles;
    }
    
    /**
     * Gets the power-ups.
     */
    public List<PowerUp> getPowerUps() {
        return powerUps;
    }
    
    /**
     * Gets the player snake.
     */
    public Snake getPlayerSnake() {
        for (Snake snake : snakes) {
            if (!snake.isAI()) {
                return snake;
            }
        }
        return null;
    }
    
    /**
     * Gets the score.
     */
    public int getScore() {
        return score;
    }
    
    /**
     * Checks if the game is over.
     */
    public boolean isGameOver() {
        return isGameOver;
    }
    
    /**
     * Restarts the game.
     */
    public void restart() {
        initGame();
        isGameOver = false;
    }
    
    /**
     * Interface for game callbacks.
     */
    public interface GameCallbacks {
        void onScoreChanged(int playerId, int score);
        void onGameOver(int winnerId);
        void onPausePressed();
        void onPowerUpSpawned(String message);
    }
}
