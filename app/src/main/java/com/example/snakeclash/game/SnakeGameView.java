package com.example.snakeclash.game;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.view.MotionEvent;
import android.view.SurfaceHolder;
import android.view.SurfaceView;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Main game view for Snake Clash.
 */
public class SnakeGameView extends SurfaceView implements SurfaceHolder.Callback, Runnable {
    // Game thread
    private Thread gameThread;
    private boolean isRunning;

    // Game state
    private boolean isGameOver;
    private int score;

    // Game objects
    private List<Snake> snakes;
    private List<Food> foods;
    private List<Obstacle> obstacles;
    private List<PowerUp> powerUps;

    // Game settings
    private static final int GRID_SIZE = 20;
    private static final int MAX_SNAKES = 6;
    private static final int MAX_FOODS = 20;
    private static final int MAX_OBSTACLES = 15; // Maximum number of obstacle blocks
    private static final float FOOD_SPAWN_INTERVAL = 2f; // seconds
    private static final float POWERUP_SPAWN_INTERVAL = 60f; // 1 minute
    private static final float INITIAL_POWERUP_DELAY = 15f; // 15 seconds before first power-up
    private float foodSpawnTimer;
    private float powerUpSpawnTimer;

    // Rendering
    private Paint paint;
    private Paint gridPaint;
    private Paint hudPaint;
    private Paint instructionsPaint;
    private int screenWidth;
    private int screenHeight;
    private float cellSize;

    // UI elements
    private RectF pauseButton;
    private boolean showInstructions;
    private float instructionsAlpha;
    private static final float INSTRUCTIONS_FADE_SPEED = 0.5f; // Alpha units per second

    // Power-up notification
    private boolean showPowerUpNotification;
    private String powerUpNotificationMessage;
    private float powerUpNotificationAlpha;
    private static final float NOTIFICATION_FADE_SPEED = 0.3f; // Alpha units per second
    private static final float NOTIFICATION_DURATION = 3.0f; // seconds

    // Game callbacks
    private GameCallbacks callbacks;

    /**
     * Constructor.
     */
    public SnakeGameView(Context context) {
        super(context);

        // Initialize surface holder
        getHolder().addCallback(this);

        // Initialize main paint
        paint = new Paint();
        paint.setAntiAlias(true);

        // Initialize grid paint
        gridPaint = new Paint();
        gridPaint.setColor(Color.argb(30, 255, 255, 255)); // Very light grid
        gridPaint.setStrokeWidth(1);
        gridPaint.setStyle(Paint.Style.STROKE);

        // Initialize HUD paint
        hudPaint = new Paint();
        hudPaint.setAntiAlias(true);
        hudPaint.setTextSize(40);
        hudPaint.setColor(Color.WHITE);
        hudPaint.setTextAlign(Paint.Align.LEFT);

        // Initialize instructions paint
        instructionsPaint = new Paint();
        instructionsPaint.setAntiAlias(true);
        instructionsPaint.setTextSize(30);
        instructionsPaint.setColor(Color.WHITE);
        instructionsPaint.setTextAlign(Paint.Align.CENTER);

        // Initialize UI elements
        pauseButton = new RectF();
        showInstructions = true;
        instructionsAlpha = 1.0f;

        // Initialize notification
        showPowerUpNotification = false;
        powerUpNotificationMessage = "";
        powerUpNotificationAlpha = 0.0f;

        // Initialize game
        initGame();
    }

    /**
     * Initializes the game.
     */
    private void initGame() {
        // Initialize game state
        isGameOver = false;
        score = 0;
        foodSpawnTimer = 0;
        powerUpSpawnTimer = POWERUP_SPAWN_INTERVAL - INITIAL_POWERUP_DELAY; // Start with initial delay

        // Initialize game objects
        snakes = new ArrayList<>();
        foods = new ArrayList<>();
        obstacles = new ArrayList<>();
        powerUps = new ArrayList<>();

        // Get effective grid size
        int[] gridSizes = getEffectiveGridSize();
        int effectiveGridWidth = gridSizes[0];
        int effectiveGridHeight = gridSizes[1];

        // Create player snake
        Snake playerSnake = new Snake(0, effectiveGridWidth / 2, effectiveGridHeight / 2, Color.GREEN, false);
        snakes.add(playerSnake);

        // Create AI snakes
        for (int i = 1; i < MAX_SNAKES; i++) {
            int x = new Random().nextInt(effectiveGridWidth);
            int y = new Random().nextInt(effectiveGridHeight);

            // Generate random color
            int r = 100 + new Random().nextInt(155);
            int g = 100 + new Random().nextInt(155);
            int b = 100 + new Random().nextInt(155);
            int color = Color.rgb(r, g, b);

            Snake aiSnake = new Snake(i, x, y, color, true);
            snakes.add(aiSnake);
        }

        // Create obstacles
        createObstacles(effectiveGridWidth, effectiveGridHeight);

        // Create initial food
        for (int i = 0; i < MAX_FOODS / 2; i++) {
            spawnFood();
        }

        // Update score
        if (callbacks != null) {
            callbacks.onScoreChanged(0, score);
        }
    }

    /**
     * Creates random obstacles in the game world.
     */
    private void createObstacles(int gridWidth, int gridHeight) {
        Random random = new Random();

        // Determine number of obstacle patterns to create
        int numPatterns = 2 + random.nextInt(3); // 2-4 patterns

        for (int pattern = 0; pattern < numPatterns; pattern++) {
            // Choose a random pattern type
            int patternType = random.nextInt(4);

            // Choose a random position for the pattern center
            int centerX = gridWidth / 4 + random.nextInt(gridWidth / 2);
            int centerY = gridHeight / 4 + random.nextInt(gridHeight / 2);

            // Create the pattern
            switch (patternType) {
                case 0: // Horizontal wall
                    int wallLength = 3 + random.nextInt(5); // 3-7 blocks
                    int wallX = centerX - wallLength / 2;
                    for (int i = 0; i < wallLength; i++) {
                        if (isValidObstaclePosition(wallX + i, centerY, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(wallX + i, centerY, ObstacleType.BLOCK));
                        }
                    }
                    break;

                case 1: // Vertical wall
                    wallLength = 3 + random.nextInt(5); // 3-7 blocks
                    int wallY = centerY - wallLength / 2;
                    for (int i = 0; i < wallLength; i++) {
                        if (isValidObstaclePosition(centerX, wallY + i, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX, wallY + i, ObstacleType.BLOCK));
                        }
                    }
                    break;

                case 2: // L-shaped wall
                    int hLength = 3 + random.nextInt(3); // 3-5 blocks
                    int vLength = 3 + random.nextInt(3); // 3-5 blocks
                    for (int i = 0; i < hLength; i++) {
                        if (isValidObstaclePosition(centerX + i, centerY, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX + i, centerY, ObstacleType.BLOCK));
                        }
                    }
                    for (int i = 0; i < vLength; i++) {
                        if (isValidObstaclePosition(centerX, centerY + i, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX, centerY + i, ObstacleType.BLOCK));
                        }
                    }
                    break;

                case 3: // Small room (square with opening)
                    int size = 3 + random.nextInt(2); // 3-4 blocks per side
                    int openingPos = random.nextInt(4); // 0=top, 1=right, 2=bottom, 3=left
                    int openingOffset = random.nextInt(size - 1); // Position of the opening

                    // Top wall
                    for (int i = 0; i < size; i++) {
                        if (openingPos == 0 && i == openingOffset) continue; // Skip for opening
                        if (isValidObstaclePosition(centerX - size/2 + i, centerY - size/2, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX - size/2 + i, centerY - size/2, ObstacleType.BLOCK));
                        }
                    }

                    // Right wall
                    for (int i = 0; i < size; i++) {
                        if (openingPos == 1 && i == openingOffset) continue; // Skip for opening
                        if (isValidObstaclePosition(centerX + size/2, centerY - size/2 + i, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX + size/2, centerY - size/2 + i, ObstacleType.BLOCK));
                        }
                    }

                    // Bottom wall
                    for (int i = 0; i < size; i++) {
                        if (openingPos == 2 && i == openingOffset) continue; // Skip for opening
                        if (isValidObstaclePosition(centerX - size/2 + i, centerY + size/2, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX - size/2 + i, centerY + size/2, ObstacleType.BLOCK));
                        }
                    }

                    // Left wall
                    for (int i = 0; i < size; i++) {
                        if (openingPos == 3 && i == openingOffset) continue; // Skip for opening
                        if (isValidObstaclePosition(centerX - size/2, centerY - size/2 + i, gridWidth, gridHeight)) {
                            obstacles.add(new Obstacle(centerX - size/2, centerY - size/2 + i, ObstacleType.BLOCK));
                        }
                    }
                    break;
            }
        }

        // Ensure we don't exceed the maximum number of obstacles
        while (obstacles.size() > MAX_OBSTACLES) {
            obstacles.remove(obstacles.size() - 1);
        }
    }

    /**
     * Checks if a position is valid for placing an obstacle.
     */
    private boolean isValidObstaclePosition(int x, int y, int gridWidth, int gridHeight) {
        // Check grid boundaries
        if (x < 0 || x >= gridWidth || y < 0 || y >= gridHeight) {
            return false;
        }

        // Check if position is already occupied by a snake
        for (Snake snake : snakes) {
            for (Point segment : snake.getBody()) {
                // Keep some distance from snake starting positions
                if (Math.abs(segment.x - x) < 3 && Math.abs(segment.y - y) < 3) {
                    return false;
                }
            }
        }

        // Check if position is already occupied by another obstacle
        for (Obstacle obstacle : obstacles) {
            if (obstacle.x == x && obstacle.y == y) {
                return false;
            }
        }

        return true;
    }

    /**
     * Spawns a new food item.
     */
    private void spawnFood() {
        if (foods.size() >= MAX_FOODS) return;

        // Get effective grid size
        int[] gridSizes = getEffectiveGridSize();
        int effectiveGridWidth = gridSizes[0];
        int effectiveGridHeight = gridSizes[1];

        // Try to find a valid position (max 10 attempts)
        for (int attempt = 0; attempt < 10; attempt++) {
            // Generate random position
            int x = new Random().nextInt(effectiveGridWidth);
            int y = new Random().nextInt(effectiveGridHeight);

            // Check if position is valid
            if (isValidFoodPosition(x, y)) {
                // Create new food
                int r = 100 + new Random().nextInt(155);
                int g = 100 + new Random().nextInt(155);
                int b = 100 + new Random().nextInt(155);
                int color = Color.rgb(r, g, b);

                Food food = new Food(x, y, color);
                foods.add(food);
                return;
            }
        }
    }

    /**
     * Checks if a position is valid for placing food or power-ups.
     */
    private boolean isValidFoodPosition(int x, int y) {
        // Check if position is occupied by a snake
        for (Snake snake : snakes) {
            for (Point point : snake.getBody()) {
                if (point.x == x && point.y == y) {
                    return false;
                }
            }
        }

        // Check if position is occupied by existing food
        for (Food food : foods) {
            if (food.x == x && food.y == y) {
                return false;
            }
        }

        // Check if position is occupied by a power-up
        for (PowerUp powerUp : powerUps) {
            if (powerUp.x == x && powerUp.y == y) {
                return false;
            }
        }

        // Check if position is occupied by an obstacle
        for (Obstacle obstacle : obstacles) {
            if (obstacle.x == x && obstacle.y == y) {
                return false;
            }
        }

        return true;
    }

    /**
     * Spawns a new power-up at a random location.
     */
    private void spawnPowerUp() {
        // Only one speed power-up at a time
        for (PowerUp powerUp : powerUps) {
            if (powerUp.type == PowerUpType.SPEED && !powerUp.isActive) {
                return; // Already have an inactive speed power-up
            }
        }

        // Get effective grid size
        int[] gridSizes = getEffectiveGridSize();
        int effectiveGridWidth = gridSizes[0];
        int effectiveGridHeight = gridSizes[1];

        // Try to find a valid position (max 20 attempts to ensure randomness)
        Random random = new Random();
        for (int attempt = 0; attempt < 20; attempt++) {
            // Generate random position
            // Avoid edges by using a smaller range
            int x = 2 + random.nextInt(effectiveGridWidth - 4);
            int y = 2 + random.nextInt(effectiveGridHeight - 4);

            // Check if position is valid
            if (isValidFoodPosition(x, y)) {
                // Create new power-up
                PowerUp powerUp = new PowerUp(x, y, PowerUpType.SPEED);
                powerUps.add(powerUp);

                // Show notification about power-up spawn
                String message = "Speed power-up has appeared!";
                showPowerUpNotification(message);

                // Also notify through callback
                if (callbacks != null) {
                    callbacks.onPowerUpSpawned(message);
                }

                return;
            }
        }
    }

    /**
     * Updates the game state.
     */
    private void update(float deltaTime) {
        if (isGameOver) return;

        // Update instructions fade-out
        if (showInstructions) {
            instructionsAlpha -= INSTRUCTIONS_FADE_SPEED * deltaTime;
            if (instructionsAlpha <= 0) {
                instructionsAlpha = 0;
                showInstructions = false;
            }
        }

        // Update power-up notification
        if (showPowerUpNotification) {
            powerUpNotificationAlpha -= NOTIFICATION_FADE_SPEED * deltaTime;
            if (powerUpNotificationAlpha <= 0) {
                powerUpNotificationAlpha = 0;
                showPowerUpNotification = false;
            }
        }

        // Update food spawn timer
        foodSpawnTimer += deltaTime;
        if (foodSpawnTimer >= FOOD_SPAWN_INTERVAL) {
            foodSpawnTimer = 0;
            spawnFood();
        }

        // Update power-up spawn timer
        powerUpSpawnTimer += deltaTime;
        if (powerUpSpawnTimer >= POWERUP_SPAWN_INTERVAL) {
            powerUpSpawnTimer = 0;
            spawnPowerUp();
        }

        // Update food animations
        for (Food food : foods) {
            food.update(deltaTime);
        }

        // Update power-ups
        for (int i = powerUps.size() - 1; i >= 0; i--) {
            PowerUp powerUp = powerUps.get(i);
            powerUp.update(deltaTime);

            // Remove expired active power-ups
            if (powerUp.isExpired()) {
                // Deactivate the power-up effect
                if (powerUp.type == PowerUpType.SPEED) {
                    // Reset speed for all snakes
                    for (Snake snake : snakes) {
                        snake.resetSpeed();
                    }
                }

                powerUps.remove(i);
            }
        }

        // Update snakes
        for (Snake snake : snakes) {
            snake.update(deltaTime, foods, snakes);

            // Check for power-up collision
            checkPowerUpCollision(snake);

            // Update score for player
            if (!snake.isAI() && snake == snakes.get(0)) {
                score = snake.getSize();
                if (callbacks != null) {
                    callbacks.onScoreChanged(0, score);
                }
            }
        }

        // Check for game over condition
        checkGameOver();
    }

    /**
     * Checks if a snake has collided with a power-up.
     */
    private void checkPowerUpCollision(Snake snake) {
        if (snake.getBody().isEmpty()) return;

        Point head = snake.getHead();

        for (int i = powerUps.size() - 1; i >= 0; i--) {
            PowerUp powerUp = powerUps.get(i);

            // Skip active power-ups
            if (powerUp.isActive) continue;

            // Check collision
            if (head.x == powerUp.x && head.y == powerUp.y) {
                // Apply power-up effect
                if (powerUp.type == PowerUpType.SPEED) {
                    // Double the snake's speed
                    snake.doubleSpeed();
                }

                // Activate the power-up
                powerUp.activate();

                // Show visual feedback
                snake.setSpeedBoost(true);

                break;
            }
        }
    }

    /**
     * Checks if the game is over.
     */
    private void checkGameOver() {
        // Game is over if player is dead or has only head left
        Snake playerSnake = snakes.get(0);
        if (playerSnake.getSize() <= 1) {
            isGameOver = true;
            if (callbacks != null) {
                callbacks.onGameOver(-1);
            }
        }
    }

    /**
     * Renders the game.
     */
    private void render(Canvas canvas) {
        if (canvas == null) return;

        // Draw enhanced background
        GraphicsUtils.drawEnhancedBackground(canvas, screenWidth, screenHeight);

        // Get effective grid size
        int[] gridSizes = getEffectiveGridSize();
        int effectiveGridWidth = gridSizes[0];
        int effectiveGridHeight = gridSizes[1];
        boolean isPortrait = screenHeight > screenWidth;

        // Calculate cell size to use maximum screen area
        float horizontalCellSize = screenWidth / (float) effectiveGridWidth;
        float verticalCellSize = screenHeight / (float) effectiveGridHeight;
        cellSize = Math.min(horizontalCellSize, verticalCellSize);

        // Calculate game area bounds to fill maximum space
        float gameWidth = cellSize * effectiveGridWidth;
        float gameHeight = cellSize * effectiveGridHeight;
        float offsetX = (screenWidth - gameWidth) / 2;
        float offsetY = (screenHeight - gameHeight) / 2;

        // Draw grid pattern
        drawGrid(canvas, offsetX, offsetY, gameWidth, gameHeight, effectiveGridWidth, effectiveGridHeight);

        // Draw boundary
        paint.setColor(Color.rgb(100, 255, 100)); // Bright green border
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(3);
        canvas.drawRect(offsetX, offsetY, offsetX + gameWidth, offsetY + gameHeight, paint);
        paint.setStyle(Paint.Style.FILL);

        // Render obstacles (draw these first as they're in the background)
        for (Obstacle obstacle : obstacles) {
            obstacle.render(canvas, paint, cellSize, offsetX, offsetY);
        }

        // Render food
        for (Food food : foods) {
            food.render(canvas, paint, cellSize, offsetX, offsetY);
        }

        // Render power-ups
        for (PowerUp powerUp : powerUps) {
            if (!powerUp.isActive) { // Only render inactive (collectible) power-ups
                powerUp.render(canvas, paint, cellSize, offsetX, offsetY);
            }
        }

        // Render snakes
        for (Snake snake : snakes) {
            snake.render(canvas, paint, cellSize, offsetX, offsetY);
        }

        // Draw HUD elements
        drawHUD(canvas, offsetX, offsetY, gameWidth, gameHeight, isPortrait);

        // Draw instructions if needed
        if (showInstructions) {
            drawInstructions(canvas);
        }

        // Draw power-up notification if needed
        if (showPowerUpNotification) {
            drawPowerUpNotification(canvas);
        }

        // Render game over message if needed
        if (isGameOver) {
            drawGameOver(canvas, isPortrait);
        }
    }

    /**
     * Draws the grid pattern in the background.
     */
    private void drawGrid(Canvas canvas, float offsetX, float offsetY, float gameWidth, float gameHeight,
                          int gridWidth, int gridHeight) {
        // Draw vertical grid lines
        for (int i = 0; i <= gridWidth; i++) {
            float x = offsetX + i * cellSize;
            canvas.drawLine(x, offsetY, x, offsetY + gameHeight, gridPaint);
        }

        // Draw horizontal grid lines
        for (int i = 0; i <= gridHeight; i++) {
            float y = offsetY + i * cellSize;
            canvas.drawLine(offsetX, y, offsetX + gameWidth, y, gridPaint);
        }
    }

    /**
     * Draws the HUD elements (score, level, pause button, and power-up timer).
     */
    private void drawHUD(Canvas canvas, float offsetX, float offsetY, float gameWidth, float gameHeight, boolean isPortrait) {
        // Use enhanced HUD rendering
        GraphicsUtils.drawEnhancedHUD(canvas, paint, offsetX, offsetY, gameWidth, gameHeight, score, isPortrait);

        // Draw level indicator on the right side
        paint.setColor(Color.argb(100, 0, 0, 0));
        RectF levelPanel = new RectF(offsetX + gameWidth - 120, offsetY - 60, offsetX + gameWidth, offsetY - 5);
        canvas.drawRoundRect(levelPanel, 10, 10, paint);

        hudPaint.setTextAlign(Paint.Align.CENTER);
        canvas.drawText("Level 1", offsetX + gameWidth - 60, offsetY - 20, hudPaint);
        hudPaint.setTextAlign(Paint.Align.LEFT);

        // Draw pause button
        paint.setColor(Color.argb(100, 0, 0, 0));
        pauseButton.set(offsetX + gameWidth + 10, offsetY, offsetX + gameWidth + 60, offsetY + 50);
        canvas.drawRoundRect(pauseButton, 10, 10, paint);

        // Draw pause icon
        paint.setColor(Color.WHITE);
        paint.setStrokeWidth(5);
        float pauseX = pauseButton.centerX();
        float pauseY = pauseButton.centerY();
        canvas.drawLine(pauseX - 7, pauseY - 10, pauseX - 7, pauseY + 10, paint);
        canvas.drawLine(pauseX + 7, pauseY - 10, pauseX + 7, pauseY + 10, paint);

        // Draw power-up timer if active
        drawPowerUpTimer(canvas, offsetX, offsetY, gameWidth);
    }

    /**
     * Draws the power-up timer if any power-up is active.
     */
    private void drawPowerUpTimer(Canvas canvas, float offsetX, float offsetY, float gameWidth) {
        // Check if any snake has an active power-up
        boolean hasActivePowerUp = false;
        float remainingTime = 0;

        // Find active power-ups
        for (PowerUp powerUp : powerUps) {
            if (powerUp.isActive) {
                hasActivePowerUp = true;
                remainingTime = PowerUp.ACTIVE_DURATION - powerUp.activeTime;
                break;
            }
        }

        if (hasActivePowerUp) {
            // Draw timer panel in the center top
            float panelWidth = 180;
            float panelHeight = 50;
            float panelX = offsetX + (gameWidth - panelWidth) / 2;

            // Draw background panel
            paint.setColor(Color.argb(180, 0, 100, 150)); // Cyan-blue background
            RectF timerPanel = new RectF(panelX, offsetY - 60, panelX + panelWidth, offsetY - 5);
            canvas.drawRoundRect(timerPanel, 10, 10, paint);

            // Draw lightning bolt icon
            paint.setColor(Color.CYAN);
            float iconX = panelX + 25;
            float iconY = offsetY - 32;
            float iconSize = 15;

            Path path = new Path();
            path.moveTo(iconX, iconY - iconSize);
            path.lineTo(iconX - iconSize/2, iconY);
            path.lineTo(iconX, iconY - iconSize/3);
            path.lineTo(iconX, iconY + iconSize);
            path.lineTo(iconX + iconSize/2, iconY);
            path.lineTo(iconX, iconY + iconSize/3);
            path.close();
            canvas.drawPath(path, paint);

            // Draw countdown text
            hudPaint.setTextSize(30);
            hudPaint.setColor(Color.WHITE);
            hudPaint.setTextAlign(Paint.Align.LEFT);
            String timeText = "Speed: " + (int)Math.ceil(remainingTime) + "s";
            canvas.drawText(timeText, panelX + 50, offsetY - 20, hudPaint);

            // Draw progress bar
            float progress = remainingTime / PowerUp.ACTIVE_DURATION;
            float barWidth = (panelWidth - 20) * progress;

            // Background bar
            paint.setColor(Color.argb(100, 255, 255, 255));
            RectF barBg = new RectF(panelX + 10, offsetY - 15, panelX + panelWidth - 10, offsetY - 10);
            canvas.drawRect(barBg, paint);

            // Progress bar
            paint.setColor(Color.WHITE);
            RectF bar = new RectF(panelX + 10, offsetY - 15, panelX + 10 + barWidth, offsetY - 10);
            canvas.drawRect(bar, paint);
        }
    }

    /**
     * Draws the instructions overlay.
     */
    private void drawInstructions(Canvas canvas) {
        // Set alpha for fading effect
        instructionsPaint.setAlpha((int)(instructionsAlpha * 255));

        // Draw semi-transparent background
        paint.setColor(Color.argb((int)(instructionsAlpha * 128), 0, 0, 0));
        canvas.drawRect(0, screenHeight / 2 - 100, screenWidth, screenHeight / 2 + 100, paint);

        // Draw instructions text
        canvas.drawText("Level 1: Use swipe gestures to navigate", screenWidth / 2, screenHeight / 2 - 50, instructionsPaint);
        canvas.drawText("Eat food to grow, avoid walls and other snakes", screenWidth / 2, screenHeight / 2, instructionsPaint);
        canvas.drawText("Bigger snakes can eat smaller ones!", screenWidth / 2, screenHeight / 2 + 50, instructionsPaint);
    }

    /**
     * Draws the power-up notification.
     */
    private void drawPowerUpNotification(Canvas canvas) {
        if (!showPowerUpNotification) return;

        // Set up paint
        Paint notificationPaint = new Paint(instructionsPaint);
        notificationPaint.setTextSize(40);
        notificationPaint.setColor(Color.CYAN);
        notificationPaint.setAlpha((int)(powerUpNotificationAlpha * 255));

        // Draw semi-transparent background
        paint.setColor(Color.argb((int)(powerUpNotificationAlpha * 100), 0, 0, 50)); // Dark blue tint
        RectF notificationBg = new RectF(0, 50, screenWidth, 150);
        canvas.drawRoundRect(notificationBg, 20, 20, paint);

        // Draw lightning bolt icon
        paint.setColor(Color.CYAN);
        paint.setAlpha((int)(powerUpNotificationAlpha * 255));

        // Draw a small lightning bolt
        Path path = new Path();
        float centerX = 80;
        float centerY = 100;
        float size = 30;

        path.moveTo(centerX, centerY - size);
        path.lineTo(centerX - size/2, centerY);
        path.lineTo(centerX, centerY - size/3);
        path.lineTo(centerX, centerY + size);
        path.lineTo(centerX + size/2, centerY);
        path.lineTo(centerX, centerY + size/3);
        path.close();

        canvas.drawPath(path, paint);

        // Draw notification text
        canvas.drawText(powerUpNotificationMessage, screenWidth / 2, 100, notificationPaint);
    }

    /**
     * Draws the game over screen.
     */
    private void drawGameOver(Canvas canvas, boolean isPortrait) {
        // Use enhanced game over screen
        GraphicsUtils.drawEnhancedGameOver(canvas, paint, screenWidth, screenHeight, score, isPortrait);
    }

    @Override
    public void surfaceCreated(SurfaceHolder holder) {
        // Start the game thread
        isRunning = true;
        gameThread = new Thread(this);
        gameThread.start();
    }

    @Override
    public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
        // Update screen dimensions
        screenWidth = width;
        screenHeight = height;
    }

    @Override
    public void surfaceDestroyed(SurfaceHolder holder) {
        // Stop the game thread
        isRunning = false;
        try {
            gameThread.join();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void run() {
        // Game loop variables
        long lastTime = System.nanoTime();
        final double amountOfTicks = 60.0;
        final double ns = 1000000000 / amountOfTicks;
        double delta = 0;

        while (isRunning) {
            // Calculate delta time
            long now = System.nanoTime();
            delta += (now - lastTime) / ns;
            lastTime = now;

            // Update and render at fixed time step
            if (delta >= 1) {
                update((float) (1.0 / amountOfTicks));

                // Render
                Canvas canvas = null;
                try {
                    canvas = getHolder().lockCanvas();
                    if (canvas != null) {
                        render(canvas);
                    }
                } finally {
                    if (canvas != null) {
                        getHolder().unlockCanvasAndPost(canvas);
                    }
                }

                delta--;
            }
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            // Get touch position
            float x = event.getX();
            float y = event.getY();

            // Hide instructions on first touch
            if (showInstructions) {
                instructionsAlpha = 0;
                showInstructions = false;
            }

            if (isGameOver) {
                // Check if restart button was pressed
                float textSize = screenHeight > screenWidth ? 60 : 50;
                float yPosition = screenHeight / 2 - 50;
                float spacing = screenHeight > screenWidth ? 70 : 60;

                RectF restartButton = new RectF(
                    screenWidth / 2 - 100,
                    yPosition + spacing * 2 - 25,
                    screenWidth / 2 + 100,
                    yPosition + spacing * 2 + 25
                );

                if (restartButton.contains(x, y)) {
                    // Restart the game
                    initGame();
                    isGameOver = false;
                }
                return true;
            }

            // Check if pause button was pressed
            if (pauseButton.contains(x, y)) {
                // Handle pause button press
                if (callbacks != null) {
                    callbacks.onPausePressed();
                }
                return true;
            }

            // Get player snake
            Snake playerSnake = snakes.get(0);
            if (playerSnake != null) {
                // Get effective grid size
                int[] gridSizes = getEffectiveGridSize();
                int effectiveGridWidth = gridSizes[0];
                int effectiveGridHeight = gridSizes[1];

                // Calculate game area bounds
                float gameWidth = cellSize * effectiveGridWidth;
                float gameHeight = cellSize * effectiveGridHeight;
                float offsetX = (screenWidth - gameWidth) / 2;
                float offsetY = (screenHeight - gameHeight) / 2;

                // Calculate direction to move
                Point head = playerSnake.getHead();
                float centerX = offsetX + head.x * cellSize + cellSize / 2;
                float centerY = offsetY + head.y * cellSize + cellSize / 2;

                // Calculate angle
                double angle = Math.atan2(y - centerY, x - centerX);

                // Convert angle to direction
                if (angle >= -Math.PI / 4 && angle < Math.PI / 4) {
                    playerSnake.setDirection(Direction.RIGHT);
                } else if (angle >= Math.PI / 4 && angle < 3 * Math.PI / 4) {
                    playerSnake.setDirection(Direction.DOWN);
                } else if (angle >= 3 * Math.PI / 4 || angle < -3 * Math.PI / 4) {
                    playerSnake.setDirection(Direction.LEFT);
                } else {
                    playerSnake.setDirection(Direction.UP);
                }
            }
        }

        return true;
    }

    /**
     * Handles key events for keyboard control.
     */
    public boolean onKeyDown(int keyCode, android.view.KeyEvent event) {
        // Get player snake
        Snake playerSnake = snakes.get(0);
        if (playerSnake != null) {
            switch (keyCode) {
                case android.view.KeyEvent.KEYCODE_DPAD_UP:
                    playerSnake.setDirection(Direction.UP);
                    return true;
                case android.view.KeyEvent.KEYCODE_DPAD_DOWN:
                    playerSnake.setDirection(Direction.DOWN);
                    return true;
                case android.view.KeyEvent.KEYCODE_DPAD_LEFT:
                    playerSnake.setDirection(Direction.LEFT);
                    return true;
                case android.view.KeyEvent.KEYCODE_DPAD_RIGHT:
                    playerSnake.setDirection(Direction.RIGHT);
                    return true;
            }
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * Sets the game callbacks.
     */
    public void setCallbacks(GameCallbacks callbacks) {
        this.callbacks = callbacks;
    }

    /**
     * Shows a notification about a power-up spawning.
     */
    public void showPowerUpNotification(String message) {
        powerUpNotificationMessage = message;
        showPowerUpNotification = true;
        powerUpNotificationAlpha = 1.0f;
    }

    /**
     * Pauses the game.
     */
    public void pause() {
        isRunning = false;
        try {
            if (gameThread != null) {
                gameThread.join();
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * Resumes the game.
     */
    public void resume() {
        isRunning = true;
        gameThread = new Thread(this);
        gameThread.start();
    }

    /**
     * Restarts the game.
     */
    public void restart() {
        initGame();
        isGameOver = false;
    }

    /**
     * Gets the effective grid size based on the current orientation.
     * @return An array with [effectiveGridWidth, effectiveGridHeight]
     */
    private int[] getEffectiveGridSize() {
        boolean isPortrait = screenHeight > screenWidth;
        int effectiveGridWidth = isPortrait ? GRID_SIZE : (int)(GRID_SIZE * 1.5);
        int effectiveGridHeight = isPortrait ? (int)(GRID_SIZE * 1.5) : GRID_SIZE;
        return new int[] { effectiveGridWidth, effectiveGridHeight };
    }

    /**
     * Interface for game callbacks.
     */
    public interface GameCallbacks {
        void onScoreChanged(int playerId, int score);
        void onGameOver(int winnerId);
        void onPausePressed();
        void onPowerUpSpawned(String message);
    }

    /**
     * Represents a point in the grid.
     */
    private class Point {
        int x, y;

        Point(int x, int y) {
            this.x = x;
            this.y = y;
        }
    }

    /**
     * Represents a food item.
     */
    private class Food {
        int x, y;
        int color;
        float pulseTime;
        float pulseScale;
        float spawnTime;
        boolean isSpawning;
        private static final float SPAWN_DURATION = 0.5f; // seconds

        Food(int x, int y, int color) {
            this.x = x;
            this.y = y;
            this.color = color;
            this.pulseTime = 0;
            this.pulseScale = 0.0f; // Start small for spawn animation
            this.spawnTime = 0;
            this.isSpawning = true;
        }

        void update(float deltaTime) {
            if (isSpawning) {
                // Handle spawn animation
                spawnTime += deltaTime;
                if (spawnTime >= SPAWN_DURATION) {
                    isSpawning = false;
                    pulseScale = 1.0f;
                } else {
                    // Grow from 0 to 1 during spawn
                    pulseScale = spawnTime / SPAWN_DURATION;
                }
            } else {
                // Normal pulsing animation
                pulseTime += deltaTime * 2;
                pulseScale = 0.8f + 0.2f * (float) Math.sin(pulseTime);
            }
        }

        void render(Canvas canvas, Paint paint, float cellSize, float offsetX, float offsetY) {
            // Base color
            paint.setColor(color);

            float radius = cellSize * 0.4f * pulseScale;
            float centerX = offsetX + x * cellSize + cellSize / 2;
            float centerY = offsetY + y * cellSize + cellSize / 2;

            // Draw main circle
            canvas.drawCircle(centerX, centerY, radius, paint);

            // Draw highlight (makes food look more 3D)
            paint.setColor(Color.argb(100, 255, 255, 255));
            canvas.drawCircle(centerX - radius * 0.3f, centerY - radius * 0.3f, radius * 0.3f, paint);

            // If spawning, add a glow effect
            if (isSpawning) {
                // Outer glow
                for (int i = 0; i < 3; i++) {
                    float alpha = 150 - i * 50;
                    float glowRadius = radius * (1.0f + 0.2f * i);
                    paint.setColor(Color.argb((int)alpha, Color.red(color), Color.green(color), Color.blue(color)));
                    canvas.drawCircle(centerX, centerY, glowRadius, paint);
                }
            }
        }
    }

    /**
     * Represents an obstacle (wall) in the game.
     */
    private class Obstacle {
        int x, y;
        ObstacleType type;

        Obstacle(int x, int y, ObstacleType type) {
            this.x = x;
            this.y = y;
            this.type = type;
        }

        void render(Canvas canvas, Paint paint, float cellSize, float offsetX, float offsetY) {
            // Set obstacle color (dark gray)
            paint.setColor(Color.rgb(70, 70, 70));

            float left = offsetX + x * cellSize;
            float top = offsetY + y * cellSize;
            float right = left + cellSize;
            float bottom = top + cellSize;

            // Draw the obstacle based on its type
            switch (type) {
                case BLOCK:
                    // Draw a solid block
                    canvas.drawRect(left, top, right, bottom, paint);

                    // Add a highlight on top/left edges
                    paint.setColor(Color.rgb(100, 100, 100));
                    paint.setStrokeWidth(2);
                    canvas.drawLine(left, top, right, top, paint);
                    canvas.drawLine(left, top, left, bottom, paint);

                    // Add a shadow on bottom/right edges
                    paint.setColor(Color.rgb(40, 40, 40));
                    canvas.drawLine(right, top, right, bottom, paint);
                    canvas.drawLine(left, bottom, right, bottom, paint);
                    break;
            }
        }
    }

    /**
     * Types of obstacles in the game.
     */
    private enum ObstacleType {
        BLOCK // A solid block that cannot be passed through
    }

    /**
     * Types of power-ups in the game.
     */
    private enum PowerUpType {
        SPEED // Increases snake speed temporarily
    }

    /**
     * Represents a power-up in the game.
     */
    private class PowerUp {
        int x, y;
        PowerUpType type;
        float pulseTime;
        float pulseScale;
        float spawnTime;
        boolean isSpawning;
        boolean isActive;
        float activeTime;
        static final float SPAWN_DURATION = 0.5f; // seconds
        static final float ACTIVE_DURATION = 10.0f; // seconds for speed boost
        private static final float PULSE_SPEED = 3.0f; // faster pulse than food

        PowerUp(int x, int y, PowerUpType type) {
            this.x = x;
            this.y = y;
            this.type = type;
            this.pulseTime = 0;
            this.pulseScale = 0.0f; // Start small for spawn animation
            this.spawnTime = 0;
            this.isSpawning = true;
            this.isActive = false;
            this.activeTime = 0;
        }

        void update(float deltaTime) {
            if (isActive) {
                // Update active time
                activeTime += deltaTime;
                if (activeTime >= ACTIVE_DURATION) {
                    isActive = false;
                }
                return;
            }

            if (isSpawning) {
                // Handle spawn animation
                spawnTime += deltaTime;
                if (spawnTime >= SPAWN_DURATION) {
                    isSpawning = false;
                    pulseScale = 1.0f;
                } else {
                    // Grow from 0 to 1 during spawn
                    pulseScale = spawnTime / SPAWN_DURATION;
                }
            } else {
                // Normal pulsing animation (faster than food)
                pulseTime += deltaTime * PULSE_SPEED;
                pulseScale = 0.7f + 0.3f * (float) Math.sin(pulseTime);
            }
        }

        void render(Canvas canvas, Paint paint, float cellSize, float offsetX, float offsetY) {
            // Base color based on type
            switch (type) {
                case SPEED:
                    paint.setColor(Color.CYAN); // Bright cyan for speed
                    break;
            }

            float radius = cellSize * 0.4f * pulseScale;
            float centerX = offsetX + x * cellSize + cellSize / 2;
            float centerY = offsetY + y * cellSize + cellSize / 2;

            // Draw main shape (lightning bolt for speed)
            if (type == PowerUpType.SPEED) {
                // Draw a lightning bolt shape
                Path path = new Path();
                float size = radius * 1.2f;

                // Lightning bolt shape
                path.moveTo(centerX, centerY - size);
                path.lineTo(centerX - size/2, centerY);
                path.lineTo(centerX, centerY - size/3);
                path.lineTo(centerX, centerY + size);
                path.lineTo(centerX + size/2, centerY);
                path.lineTo(centerX, centerY + size/3);
                path.close();

                canvas.drawPath(path, paint);

                // Draw outline
                Paint outlinePaint = new Paint(paint);
                outlinePaint.setStyle(Paint.Style.STROKE);
                outlinePaint.setStrokeWidth(2);
                outlinePaint.setColor(Color.WHITE);
                canvas.drawPath(path, outlinePaint);
            }

            // If spawning, add a glow effect
            if (isSpawning) {
                // Outer glow
                for (int i = 0; i < 3; i++) {
                    float alpha = 150 - i * 50;
                    float glowRadius = radius * (1.0f + 0.3f * i);
                    paint.setColor(Color.argb((int)alpha, 0, 255, 255)); // Cyan glow
                    canvas.drawCircle(centerX, centerY, glowRadius, paint);
                }
            }

            // Add electric sparks effect
            if (!isSpawning) {
                paint.setColor(Color.WHITE);
                paint.setStrokeWidth(2);

                // Draw random sparks
                Random random = new Random((long)(pulseTime * 1000));
                for (int i = 0; i < 4; i++) {
                    float angle = random.nextFloat() * 360;
                    float length = radius * (0.7f + random.nextFloat() * 0.6f);
                    float startX = centerX + (float)Math.cos(Math.toRadians(angle)) * radius * 0.8f;
                    float startY = centerY + (float)Math.sin(Math.toRadians(angle)) * radius * 0.8f;
                    float endX = centerX + (float)Math.cos(Math.toRadians(angle)) * length;
                    float endY = centerY + (float)Math.sin(Math.toRadians(angle)) * length;

                    canvas.drawLine(startX, startY, endX, endY, paint);
                }
            }
        }

        void activate() {
            isActive = true;
            activeTime = 0;
        }

        boolean isExpired() {
            return isActive && activeTime >= ACTIVE_DURATION;
        }
    }

    /**
     * Represents a direction.
     */
    private enum Direction {
        UP, DOWN, LEFT, RIGHT
    }

    /**
     * Represents a snake.
     */
    private class Snake {
        private final int id;
        private final int color;
        private final boolean isAI;
        private final List<Point> body;
        private Direction direction;
        private float moveTimer;
        private float moveInterval;
        private float normalMoveInterval; // Store the normal speed
        private float collisionCooldown;
        private float lastCollisionTime;
        private boolean hasSpeedBoost;
        private static final float COLLISION_COOLDOWN_TIME = 0.5f;
        private static final float COLLISION_FLASH_DURATION = 0.3f;

        Snake(int id, int x, int y, int color, boolean isAI) {
            this.id = id;
            this.color = color;
            this.isAI = isAI;
            this.body = new ArrayList<>();
            this.direction = Direction.values()[new Random().nextInt(Direction.values().length)];
            this.moveTimer = 0;
            this.moveInterval = isAI ? 0.3f : 0.2f; // AI moves slower
            this.normalMoveInterval = this.moveInterval; // Store normal speed
            this.collisionCooldown = 0;
            this.hasSpeedBoost = false;

            // Add initial segments
            body.add(new Point(x, y)); // Head
            addSegments(isAI ? 3 : 5); // Player starts with more segments
        }

        void update(float deltaTime, List<Food> foods, List<Snake> snakes) {
            // Update collision cooldown
            if (collisionCooldown > 0) {
                collisionCooldown -= deltaTime;
            }

            // Update move timer
            moveTimer += deltaTime;
            if (moveTimer >= moveInterval) {
                moveTimer = 0;

                // Move the snake
                move();

                // Check for obstacle collision
                if (checkObstacleCollision()) {
                    // If we hit an obstacle, don't check for other collisions
                    return;
                }

                // Check for food collision
                checkFoodCollision(foods);

                // Check for snake collision
                if (collisionCooldown <= 0) {
                    checkSnakeCollision(snakes);
                }

                // AI behavior
                if (isAI) {
                    updateAI(foods, snakes);
                }
            }
        }

        void move() {
            if (body.isEmpty()) return;

            // Get the head
            Point head = body.get(0);

            // Calculate new head position
            int newX = head.x;
            int newY = head.y;

            switch (direction) {
                case UP:
                    newY--;
                    break;
                case DOWN:
                    newY++;
                    break;
                case LEFT:
                    newX--;
                    break;
                case RIGHT:
                    newX++;
                    break;
            }

            // Determine if we're in portrait or landscape mode
            boolean isPortrait = screenHeight > screenWidth;

            // Adjust grid size based on orientation
            int effectiveGridWidth = isPortrait ? GRID_SIZE : (int)(GRID_SIZE * 1.5);
            int effectiveGridHeight = isPortrait ? (int)(GRID_SIZE * 1.5) : GRID_SIZE;

            // Wrap around grid boundaries
            newX = (newX + effectiveGridWidth) % effectiveGridWidth;
            newY = (newY + effectiveGridHeight) % effectiveGridHeight;

            // Add new head
            Point newHead = new Point(newX, newY);
            body.add(0, newHead);

            // Remove tail if not growing
            if (body.size() > 1) {
                body.remove(body.size() - 1);
            }
        }

        void checkFoodCollision(List<Food> foods) {
            if (body.isEmpty()) return;

            Point head = body.get(0);

            for (int i = 0; i < foods.size(); i++) {
                Food food = foods.get(i);

                if (head.x == food.x && head.y == food.y) {
                    // Eat the food
                    foods.remove(i);

                    // Grow the snake
                    addSegment();

                    // Increase speed slightly
                    moveInterval = Math.max(0.1f, moveInterval - 0.01f);

                    break;
                }
            }
        }

        void checkSnakeCollision(List<Snake> snakes) {
            if (body.isEmpty()) return;

            // Skip if we're in cooldown
            if (collisionCooldown > 0) return;

            Point head = body.get(0);

            for (Snake otherSnake : snakes) {
                // Skip self
                if (otherSnake.id == this.id) continue;

                // Check collision with other snake's body
                for (int i = 0; i < otherSnake.body.size(); i++) {
                    Point otherSegment = otherSnake.body.get(i);

                    if (head.x == otherSegment.x && head.y == otherSegment.y) {
                        // Handle collision based on size
                        handleSnakeCollision(otherSnake, i);
                        return; // Only handle one collision per update
                    }
                }
            }
        }

        void handleSnakeCollision(Snake otherSnake, int segmentIndex) {
            // Compare sizes
            if (body.size() > otherSnake.body.size()) {
                // We're bigger - we can eat the other snake
                if (segmentIndex == 0) {
                    // We hit the head - eat the whole snake
                    int segmentsToAdd = otherSnake.body.size();
                    otherSnake.die();
                    addSegments(segmentsToAdd);
                } else {
                    // We hit the body - eat part of the snake
                    int segmentsToAdd = otherSnake.body.size() - segmentIndex;
                    otherSnake.removeSegmentsFrom(segmentIndex);
                    addSegments(segmentsToAdd);
                }
            } else {
                // We're smaller or equal - bounce off
                bounceOff(otherSnake, segmentIndex);
            }
        }

        void bounceOff(Snake otherSnake, int segmentIndex) {
            // Get our head and the other snake's segment we collided with
            Point head = body.get(0);
            Point otherSegment = otherSnake.body.get(segmentIndex);

            // Determine bounce direction based on collision point
            if (segmentIndex == 0) {
                // Head-to-head collision - bounce in opposite direction
                switch (direction) {
                    case UP:
                        direction = Direction.DOWN;
                        break;
                    case DOWN:
                        direction = Direction.UP;
                        break;
                    case LEFT:
                        direction = Direction.RIGHT;
                        break;
                    case RIGHT:
                        direction = Direction.LEFT;
                        break;
                }
            } else {
                // We hit the other snake's body - bounce based on relative position
                // Try to find a direction away from the collision

                // Get the previous segment in the other snake to determine its body direction
                Point otherPrevSegment = null;
                if (segmentIndex < otherSnake.body.size() - 1) {
                    otherPrevSegment = otherSnake.body.get(segmentIndex + 1);
                }

                // Determine the best direction to bounce
                if (otherPrevSegment != null) {
                    // If other snake is horizontal (left/right)
                    if (otherPrevSegment.y == otherSegment.y) {
                        // Bounce vertically
                        if (head.y < otherSegment.y) {
                            direction = Direction.UP;
                        } else {
                            direction = Direction.DOWN;
                        }
                    }
                    // If other snake is vertical (up/down)
                    else if (otherPrevSegment.x == otherSegment.x) {
                        // Bounce horizontally
                        if (head.x < otherSegment.x) {
                            direction = Direction.LEFT;
                        } else {
                            direction = Direction.RIGHT;
                        }
                    }
                    // If diagonal or can't determine, just reverse direction
                    else {
                        switch (direction) {
                            case UP:
                                direction = Direction.DOWN;
                                break;
                            case DOWN:
                                direction = Direction.UP;
                                break;
                            case LEFT:
                                direction = Direction.RIGHT;
                                break;
                            case RIGHT:
                                direction = Direction.LEFT;
                                break;
                        }
                    }
                } else {
                    // Can't determine other snake's direction, just reverse
                    switch (direction) {
                        case UP:
                            direction = Direction.DOWN;
                            break;
                        case DOWN:
                            direction = Direction.UP;
                            break;
                        case LEFT:
                            direction = Direction.RIGHT;
                            break;
                        case RIGHT:
                            direction = Direction.LEFT;
                            break;
                    }
                }
            }

            // Set collision cooldown
            collisionCooldown = COLLISION_COOLDOWN_TIME;

            // Visual feedback for bounce - change color briefly
            // We'll implement this by adding a flash effect in the render method
            lastCollisionTime = moveTimer;
        }

        /**
         * Checks for collision with obstacles.
         * @return true if a collision occurred
         */
        boolean checkObstacleCollision() {
            if (body.isEmpty()) return false;

            Point head = body.get(0);

            // Check if the head collided with any obstacle
            for (Obstacle obstacle : obstacles) {
                if (head.x == obstacle.x && head.y == obstacle.y) {
                    // We hit an obstacle - bounce off
                    bounceOffObstacle(obstacle);
                    return true;
                }
            }

            return false;
        }

        /**
         * Handles bouncing off an obstacle.
         */
        void bounceOffObstacle(Obstacle obstacle) {
            // Reverse direction
            switch (direction) {
                case UP:
                    direction = Direction.DOWN;
                    break;
                case DOWN:
                    direction = Direction.UP;
                    break;
                case LEFT:
                    direction = Direction.RIGHT;
                    break;
                case RIGHT:
                    direction = Direction.LEFT;
                    break;
            }

            // Move the head away from the obstacle to prevent getting stuck
            if (!body.isEmpty()) {
                Point head = body.get(0);

                // Remove the head
                body.remove(0);

                // Add a new head in the opposite direction
                Point newHead = null;
                switch (direction) {
                    case UP:
                        newHead = new Point(head.x, head.y - 1);
                        break;
                    case DOWN:
                        newHead = new Point(head.x, head.y + 1);
                        break;
                    case LEFT:
                        newHead = new Point(head.x - 1, head.y);
                        break;
                    case RIGHT:
                        newHead = new Point(head.x + 1, head.y);
                        break;
                }

                // Get effective grid size
                int[] gridSizes = getEffectiveGridSize();
                int effectiveGridWidth = gridSizes[0];
                int effectiveGridHeight = gridSizes[1];

                // Wrap around grid boundaries
                if (newHead != null) {
                    newHead.x = (newHead.x + effectiveGridWidth) % effectiveGridWidth;
                    newHead.y = (newHead.y + effectiveGridHeight) % effectiveGridHeight;
                    body.add(0, newHead);
                }
            }

            // Set collision cooldown
            collisionCooldown = COLLISION_COOLDOWN_TIME;

            // Visual feedback
            lastCollisionTime = moveTimer;
        }

        void updateAI(List<Food> foods, List<Snake> snakes) {
            // Simple AI: find nearest food or smaller snake to chase
            if (body.isEmpty()) return;

            Point head = body.get(0);

            // Occasionally change direction randomly
            if (new Random().nextFloat() < 0.1f) {
                direction = Direction.values()[new Random().nextInt(Direction.values().length)];
                return;
            }

            // Target coordinates
            int targetX = head.x;
            int targetY = head.y;
            int minDistance = Integer.MAX_VALUE;

            // Check for nearby food
            for (Food food : foods) {
                int distance = Math.abs(head.x - food.x) + Math.abs(head.y - food.y);
                if (distance < minDistance) {
                    minDistance = distance;
                    targetX = food.x;
                    targetY = food.y;
                }
            }

            // Check for smaller snakes to chase
            for (Snake otherSnake : snakes) {
                if (otherSnake.id != this.id && otherSnake.body.size() < this.body.size()) {
                    Point otherHead = otherSnake.getHead();
                    int distance = Math.abs(head.x - otherHead.x) + Math.abs(head.y - otherHead.y);
                    if (distance < minDistance) {
                        minDistance = distance;
                        targetX = otherHead.x;
                        targetY = otherHead.y;
                    }
                }
            }

            // Check for obstacles to avoid
            Direction currentDir = direction;
            Direction[] possibleDirs = new Direction[4];
            int numDirs = 0;

            // Consider all possible directions
            for (Direction dir : Direction.values()) {
                // Skip the opposite of current direction
                if ((currentDir == Direction.UP && dir == Direction.DOWN) ||
                    (currentDir == Direction.DOWN && dir == Direction.UP) ||
                    (currentDir == Direction.LEFT && dir == Direction.RIGHT) ||
                    (currentDir == Direction.RIGHT && dir == Direction.LEFT)) {
                    continue;
                }

                // Check if moving in this direction would hit an obstacle
                Point nextPos = new Point(head.x, head.y);
                switch (dir) {
                    case UP:
                        nextPos.y--;
                        break;
                    case DOWN:
                        nextPos.y++;
                        break;
                    case LEFT:
                        nextPos.x--;
                        break;
                    case RIGHT:
                        nextPos.x++;
                        break;
                }

                // Wrap around grid boundaries
                int[] gridSizes = getEffectiveGridSize();
                nextPos.x = (nextPos.x + gridSizes[0]) % gridSizes[0];
                nextPos.y = (nextPos.y + gridSizes[1]) % gridSizes[1];

                // Check if this position has an obstacle
                boolean hasObstacle = false;
                for (Obstacle obstacle : obstacles) {
                    if (nextPos.x == obstacle.x && nextPos.y == obstacle.y) {
                        hasObstacle = true;
                        break;
                    }
                }

                if (!hasObstacle) {
                    possibleDirs[numDirs++] = dir;
                }
            }

            // If we have valid directions, choose the one closest to target
            if (numDirs > 0) {
                // Default to current direction if it's valid
                Direction bestDir = currentDir;
                int bestDistance = Integer.MAX_VALUE;

                for (int i = 0; i < numDirs; i++) {
                    Direction dir = possibleDirs[i];
                    Point nextPos = new Point(head.x, head.y);

                    switch (dir) {
                        case UP:
                            nextPos.y--;
                            break;
                        case DOWN:
                            nextPos.y++;
                            break;
                        case LEFT:
                            nextPos.x--;
                            break;
                        case RIGHT:
                            nextPos.x++;
                            break;
                    }

                    // Calculate distance to target
                    int distance = Math.abs(nextPos.x - targetX) + Math.abs(nextPos.y - targetY);
                    if (distance < bestDistance) {
                        bestDistance = distance;
                        bestDir = dir;
                    }
                }

                direction = bestDir;
            } else {
                // All directions have obstacles, just use the basic targeting
                if (Math.abs(head.x - targetX) > Math.abs(head.y - targetY)) {
                    // Move horizontally
                    if (head.x < targetX) {
                        direction = Direction.RIGHT;
                    } else if (head.x > targetX) {
                        direction = Direction.LEFT;
                    }
                } else {
                    // Move vertically
                    if (head.y < targetY) {
                        direction = Direction.DOWN;
                    } else if (head.y > targetY) {
                        direction = Direction.UP;
                    }
                }
            }
        }

        void addSegment() {
            if (body.isEmpty()) return;

            // Get the last segment
            Point last = body.get(body.size() - 1);

            // Add a new segment at the same position as the last one
            // It will be moved to the correct position on the next move
            body.add(new Point(last.x, last.y));
        }

        void addSegments(int count) {
            for (int i = 0; i < count; i++) {
                addSegment();
            }
        }

        void removeSegmentsFrom(int startIndex) {
            // Keep at least the head
            if (startIndex <= 0) startIndex = 1;

            // Remove segments
            while (body.size() > startIndex) {
                body.remove(body.size() - 1);
            }
        }

        void die() {
            removeSegmentsFrom(1);
        }

        void render(Canvas canvas, Paint paint, float cellSize, float offsetX, float offsetY) {
            // Check if we should show the bounce flash effect
            boolean showFlash = (moveTimer - lastCollisionTime) < COLLISION_FLASH_DURATION;

            // Draw body segments from tail to head
            for (int i = body.size() - 1; i >= 0; i--) {
                Point segment = body.get(i);

                float centerX = offsetX + segment.x * cellSize + cellSize / 2;
                float centerY = offsetY + segment.y * cellSize + cellSize / 2;

                if (i == 0) {
                    // This is the head - draw it differently
                    // Use a slightly larger radius and different color
                    float headRadius = cellSize * 0.45f;

                    // Create a lighter color for the head
                    int headColor;
                    if (showFlash) {
                        // Flash white when bouncing
                        headColor = Color.WHITE;
                    } else if (hasSpeedBoost) {
                        // Use cyan tint for speed boost
                        headColor = interpolateColor(lightenColor(color), Color.CYAN, 0.5f);
                    } else {
                        headColor = lightenColor(color);
                    }
                    paint.setColor(headColor);

                    // Draw the head
                    canvas.drawCircle(centerX, centerY, headRadius, paint);

                    // Draw eyes
                    paint.setColor(Color.BLACK);

                    // Calculate eye positions based on direction
                    float eyeRadius = cellSize * 0.08f;
                    float eyeOffset = cellSize * 0.15f;

                    float leftEyeX = centerX;
                    float leftEyeY = centerY;
                    float rightEyeX = centerX;
                    float rightEyeY = centerY;

                    switch (direction) {
                        case UP:
                            leftEyeX = centerX - eyeOffset;
                            leftEyeY = centerY - eyeOffset;
                            rightEyeX = centerX + eyeOffset;
                            rightEyeY = centerY - eyeOffset;
                            break;
                        case DOWN:
                            leftEyeX = centerX - eyeOffset;
                            leftEyeY = centerY + eyeOffset;
                            rightEyeX = centerX + eyeOffset;
                            rightEyeY = centerY + eyeOffset;
                            break;
                        case LEFT:
                            leftEyeX = centerX - eyeOffset;
                            leftEyeY = centerY - eyeOffset;
                            rightEyeX = centerX - eyeOffset;
                            rightEyeY = centerY + eyeOffset;
                            break;
                        case RIGHT:
                            leftEyeX = centerX + eyeOffset;
                            leftEyeY = centerY - eyeOffset;
                            rightEyeX = centerX + eyeOffset;
                            rightEyeY = centerY + eyeOffset;
                            break;
                    }

                    canvas.drawCircle(leftEyeX, leftEyeY, eyeRadius, paint);
                    canvas.drawCircle(rightEyeX, rightEyeY, eyeRadius, paint);
                } else {
                    // This is a body segment
                    float radius = cellSize * 0.4f;

                    // Use normal color or flash color
                    if (showFlash && i < 3) { // Only flash the first few segments
                        // Flash white with fading effect based on segment position
                        float flashIntensity = 1.0f - (i / 3.0f);
                        int flashColor = interpolateColor(color, Color.WHITE, flashIntensity);
                        paint.setColor(flashColor);
                    } else if (hasSpeedBoost) {
                        // Add speed effect (cyan trail)
                        float speedEffectIntensity = Math.max(0, 1.0f - (i / (float)body.size()));
                        int speedColor = interpolateColor(color, Color.CYAN, speedEffectIntensity * 0.3f);
                        paint.setColor(speedColor);

                        // Draw speed trail
                        if (i % 2 == 0) { // Every other segment
                            paint.setStyle(Paint.Style.STROKE);
                            paint.setStrokeWidth(2);
                            paint.setColor(Color.CYAN);
                            canvas.drawCircle(centerX, centerY, radius * 1.2f, paint);
                            paint.setStyle(Paint.Style.FILL);
                        }
                    } else {
                        paint.setColor(color);
                    }

                    canvas.drawCircle(centerX, centerY, radius, paint);
                }
            }

            // Draw speed boost effect
            if (hasSpeedBoost && !body.isEmpty()) {
                // Draw a trail behind the snake
                Point tail = body.get(body.size() - 1);
                float tailX = offsetX + tail.x * cellSize + cellSize / 2;
                float tailY = offsetY + tail.y * cellSize + cellSize / 2;

                // Draw speed lines
                paint.setColor(Color.CYAN);
                paint.setAlpha(100);
                paint.setStrokeWidth(2);

                // Calculate direction from second-to-last to last segment
                float dirX = 0, dirY = 0;
                if (body.size() > 1) {
                    Point secondLast = body.get(body.size() - 2);
                    dirX = tail.x - secondLast.x;
                    dirY = tail.y - secondLast.y;

                    // Normalize
                    float length = (float)Math.sqrt(dirX * dirX + dirY * dirY);
                    if (length > 0) {
                        dirX /= length;
                        dirY /= length;
                    }
                }

                // Draw multiple trail lines
                Random random = new Random(System.currentTimeMillis());
                for (int i = 0; i < 5; i++) {
                    float offsetAngle = (random.nextFloat() - 0.5f) * 30; // -15 to +15 degrees
                    float rad = (float)Math.toRadians(offsetAngle);
                    float rotDirX = (float)(dirX * Math.cos(rad) - dirY * Math.sin(rad));
                    float rotDirY = (float)(dirX * Math.sin(rad) + dirY * Math.cos(rad));

                    float length = cellSize * (1.5f + random.nextFloat() * 2.0f);
                    canvas.drawLine(
                        tailX,
                        tailY,
                        tailX - rotDirX * length,
                        tailY - rotDirY * length,
                        paint
                    );
                }
                paint.setAlpha(255); // Reset alpha
            }
        }

        Point getHead() {
            return body.isEmpty() ? null : body.get(0);
        }

        List<Point> getBody() {
            return body;
        }

        int getSize() {
            return body.size();
        }

        boolean isAI() {
            return isAI;
        }

        void setDirection(Direction direction) {
            // Prevent 180-degree turns
            if ((this.direction == Direction.UP && direction == Direction.DOWN) ||
                (this.direction == Direction.DOWN && direction == Direction.UP) ||
                (this.direction == Direction.LEFT && direction == Direction.RIGHT) ||
                (this.direction == Direction.RIGHT && direction == Direction.LEFT)) {
                return;
            }

            this.direction = direction;
        }

        /**
         * Doubles the snake's speed (for speed power-up).
         */
        void doubleSpeed() {
            // Store normal speed if not already stored
            if (!hasSpeedBoost) {
                normalMoveInterval = moveInterval;
            }

            // Double the speed (half the interval)
            moveInterval = normalMoveInterval / 2.0f;
            hasSpeedBoost = true;
        }

        /**
         * Resets the snake's speed to normal.
         */
        void resetSpeed() {
            if (hasSpeedBoost) {
                moveInterval = normalMoveInterval;
                hasSpeedBoost = false;
            }
        }

        /**
         * Sets whether the snake has a speed boost.
         */
        void setSpeedBoost(boolean hasBoost) {
            this.hasSpeedBoost = hasBoost;
        }

        /**
         * Creates a lighter version of the given color for the snake head.
         */
        private int lightenColor(int color) {
            float[] hsv = new float[3];
            Color.colorToHSV(color, hsv);

            // Increase brightness but keep the same hue
            hsv[1] *= 0.7f; // Reduce saturation a bit
            hsv[2] = Math.min(1.0f, hsv[2] * 1.3f); // Increase brightness

            return Color.HSVToColor(hsv);
        }

        /**
         * Interpolates between two colors.
         * @param color1 The first color
         * @param color2 The second color
         * @param fraction The fraction (0-1) to interpolate
         * @return The interpolated color
         */
        private int interpolateColor(int color1, int color2, float fraction) {
            float[] hsv1 = new float[3];
            float[] hsv2 = new float[3];
            Color.colorToHSV(color1, hsv1);
            Color.colorToHSV(color2, hsv2);

            // Interpolate each component
            float[] result = new float[3];
            for (int i = 0; i < 3; i++) {
                result[i] = hsv1[i] + fraction * (hsv2[i] - hsv1[i]);
            }

            return Color.HSVToColor(result);
        }
    }
}
