package com.example.snakeclash.game;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RadialGradient;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;

import java.util.Random;

/**
 * Utility class for enhanced graphics in Snake Clash.
 */
public class GraphicsUtils {
    private static final Random random = new Random();
    
    /**
     * Creates a gradient background.
     */
    public static void drawEnhancedBackground(Canvas canvas, float width, float height) {
        Paint paint = new Paint();
        
        // Create a dark gradient background
        LinearGradient gradient = new LinearGradient(
                0, 0, 
                width, height, 
                new int[] {
                        Color.rgb(0, 30, 0),  // Dark green
                        Color.rgb(0, 40, 10), // Slightly lighter green
                        Color.rgb(0, 25, 5)   // Medium dark green
                }, 
                null, 
                Shader.TileMode.CLAMP);
        
        paint.setShader(gradient);
        canvas.drawRect(0, 0, width, height, paint);
        
        // Add subtle pattern
        paint.setShader(null);
        paint.setColor(Color.argb(10, 255, 255, 255));
        
        // Draw grid pattern
        float gridSize = Math.max(width, height) / 40;
        for (int i = 0; i < width; i += gridSize) {
            canvas.drawLine(i, 0, i, height, paint);
        }
        for (int i = 0; i < height; i += gridSize) {
            canvas.drawLine(0, i, width, i, paint);
        }
    }
    
    /**
     * Draws an enhanced snake segment.
     */
    public static void drawEnhancedSnakeSegment(Canvas canvas, Paint paint, float centerX, float centerY, 
                                               float radius, int color, boolean isHead, boolean hasSpeedBoost, 
                                               boolean showFlash, Direction direction) {
        // Save original paint color
        int originalColor = paint.getColor();
        
        // Determine segment color
        int segmentColor = color;
        if (showFlash) {
            segmentColor = Color.WHITE;
        } else if (hasSpeedBoost && isHead) {
            segmentColor = interpolateColor(lightenColor(color), Color.CYAN, 0.5f);
        } else if (isHead) {
            segmentColor = lightenColor(color);
        }
        
        // Create gradient for 3D effect
        RadialGradient gradient = new RadialGradient(
                centerX - radius * 0.2f, 
                centerY - radius * 0.2f, 
                radius * 1.5f,
                new int[] {
                        lightenColor(segmentColor, 0.7f),
                        segmentColor,
                        darkenColor(segmentColor, 0.7f)
                },
                new float[] {0.3f, 0.6f, 1.0f},
                Shader.TileMode.CLAMP);
        
        paint.setShader(gradient);
        
        // Draw main circle with gradient
        canvas.drawCircle(centerX, centerY, radius, paint);
        
        // Reset shader
        paint.setShader(null);
        
        // Add highlight
        paint.setColor(Color.argb(80, 255, 255, 255));
        canvas.drawCircle(centerX - radius * 0.3f, centerY - radius * 0.3f, radius * 0.3f, paint);
        
        // If it's the head, add eyes
        if (isHead) {
            // Draw eyes based on direction
            float eyeRadius = radius * 0.2f;
            float eyeDistance = radius * 0.3f;
            
            float leftEyeX = centerX;
            float leftEyeY = centerY;
            float rightEyeX = centerX;
            float rightEyeY = centerY;
            
            switch (direction) {
                case UP:
                    leftEyeX = centerX - eyeDistance;
                    leftEyeY = centerY - eyeDistance * 0.5f;
                    rightEyeX = centerX + eyeDistance;
                    rightEyeY = centerY - eyeDistance * 0.5f;
                    break;
                case DOWN:
                    leftEyeX = centerX - eyeDistance;
                    leftEyeY = centerY + eyeDistance * 0.5f;
                    rightEyeX = centerX + eyeDistance;
                    rightEyeY = centerY + eyeDistance * 0.5f;
                    break;
                case LEFT:
                    leftEyeX = centerX - eyeDistance * 0.5f;
                    leftEyeY = centerY - eyeDistance;
                    rightEyeX = centerX - eyeDistance * 0.5f;
                    rightEyeY = centerY + eyeDistance;
                    break;
                case RIGHT:
                    leftEyeX = centerX + eyeDistance * 0.5f;
                    leftEyeY = centerY - eyeDistance;
                    rightEyeX = centerX + eyeDistance * 0.5f;
                    rightEyeY = centerY + eyeDistance;
                    break;
            }
            
            // Draw eye whites
            paint.setColor(Color.WHITE);
            canvas.drawCircle(leftEyeX, leftEyeY, eyeRadius, paint);
            canvas.drawCircle(rightEyeX, rightEyeY, eyeRadius, paint);
            
            // Draw pupils
            paint.setColor(Color.BLACK);
            canvas.drawCircle(leftEyeX, leftEyeY, eyeRadius * 0.6f, paint);
            canvas.drawCircle(rightEyeX, rightEyeY, eyeRadius * 0.6f, paint);
            
            // Add shine to eyes
            paint.setColor(Color.WHITE);
            canvas.drawCircle(leftEyeX - eyeRadius * 0.2f, leftEyeY - eyeRadius * 0.2f, eyeRadius * 0.2f, paint);
            canvas.drawCircle(rightEyeX - eyeRadius * 0.2f, rightEyeY - eyeRadius * 0.2f, eyeRadius * 0.2f, paint);
        }
        
        // Add speed effect if needed
        if (hasSpeedBoost) {
            // Draw speed trail
            paint.setColor(Color.argb(100, 0, 255, 255));
            paint.setStyle(Paint.Style.STROKE);
            paint.setStrokeWidth(2);
            
            for (int i = 0; i < 3; i++) {
                float trailRadius = radius * (1.0f + 0.15f * i);
                canvas.drawCircle(centerX, centerY, trailRadius, paint);
            }
            
            paint.setStyle(Paint.Style.FILL);
        }
        
        // Restore original paint color
        paint.setColor(originalColor);
    }
    
    /**
     * Draws enhanced food with glow and 3D effect.
     */
    public static void drawEnhancedFood(Canvas canvas, Paint paint, float centerX, float centerY, 
                                       float radius, int color, float pulseScale, boolean isSpawning) {
        // Save original paint color
        int originalColor = paint.getColor();
        
        // Create gradient for 3D effect
        RadialGradient gradient = new RadialGradient(
                centerX - radius * 0.2f, 
                centerY - radius * 0.2f, 
                radius * 1.5f,
                new int[] {
                        lightenColor(color, 0.7f),
                        color,
                        darkenColor(color, 0.7f)
                },
                new float[] {0.3f, 0.6f, 1.0f},
                Shader.TileMode.CLAMP);
        
        paint.setShader(gradient);
        
        // Draw main circle with gradient
        canvas.drawCircle(centerX, centerY, radius, paint);
        
        // Reset shader
        paint.setShader(null);
        
        // Add highlight
        paint.setColor(Color.argb(120, 255, 255, 255));
        canvas.drawCircle(centerX - radius * 0.3f, centerY - radius * 0.3f, radius * 0.3f, paint);
        
        // If spawning, add a glow effect
        if (isSpawning) {
            // Outer glow
            for (int i = 0; i < 3; i++) {
                float alpha = 150 - i * 50;
                float glowRadius = radius * (1.0f + 0.2f * i);
                paint.setColor(Color.argb((int)alpha, Color.red(color), Color.green(color), Color.blue(color)));
                canvas.drawCircle(centerX, centerY, glowRadius, paint);
            }
        }
        
        // Add sparkle effect
        paint.setColor(Color.WHITE);
        float sparkleSize = radius * 0.15f;
        canvas.drawLine(centerX - sparkleSize, centerY - sparkleSize, 
                        centerX + sparkleSize, centerY + sparkleSize, paint);
        canvas.drawLine(centerX + sparkleSize, centerY - sparkleSize, 
                        centerX - sparkleSize, centerY + sparkleSize, paint);
        
        // Restore original paint color
        paint.setColor(originalColor);
    }
    
    /**
     * Draws enhanced power-up with special effects.
     */
    public static void drawEnhancedPowerUp(Canvas canvas, Paint paint, float centerX, float centerY, 
                                          float radius, PowerUpType type, float pulseScale, boolean isSpawning) {
        // Save original paint color
        int originalColor = paint.getColor();
        
        // Determine color based on power-up type
        int color;
        switch (type) {
            case SPEED:
                color = Color.CYAN;
                break;
            default:
                color = Color.YELLOW;
                break;
        }
        
        // Draw glow effect
        for (int i = 0; i < 3; i++) {
            float alpha = 100 - i * 30;
            float glowRadius = radius * (1.0f + 0.3f * i);
            paint.setColor(Color.argb((int)alpha, Color.red(color), Color.green(color), Color.blue(color)));
            canvas.drawCircle(centerX, centerY, glowRadius, paint);
        }
        
        // Draw main shape based on power-up type
        if (type == PowerUpType.SPEED) {
            // Draw a lightning bolt shape
            paint.setColor(color);
            Path path = new Path();
            float size = radius * 1.2f;
            
            // Lightning bolt shape
            path.moveTo(centerX, centerY - size);
            path.lineTo(centerX - size/2, centerY);
            path.lineTo(centerX, centerY - size/3);
            path.lineTo(centerX, centerY + size);
            path.lineTo(centerX + size/2, centerY);
            path.lineTo(centerX, centerY + size/3);
            path.close();
            
            canvas.drawPath(path, paint);
            
            // Draw outline
            paint.setStyle(Paint.Style.STROKE);
            paint.setStrokeWidth(2);
            paint.setColor(Color.WHITE);
            canvas.drawPath(path, paint);
            paint.setStyle(Paint.Style.FILL);
        }
        
        // Add electric sparks effect
        paint.setColor(Color.WHITE);
        paint.setStrokeWidth(2);
        
        // Draw random sparks
        for (int i = 0; i < 4; i++) {
            float angle = random.nextFloat() * 360;
            float length = radius * (0.7f + random.nextFloat() * 0.6f);
            float startX = centerX + (float)Math.cos(Math.toRadians(angle)) * radius * 0.8f;
            float startY = centerY + (float)Math.sin(Math.toRadians(angle)) * radius * 0.8f;
            float endX = centerX + (float)Math.cos(Math.toRadians(angle)) * length;
            float endY = centerY + (float)Math.sin(Math.toRadians(angle)) * length;
            
            canvas.drawLine(startX, startY, endX, endY, paint);
        }
        
        // Restore original paint color
        paint.setColor(originalColor);
    }
    
    /**
     * Draws enhanced obstacle with 3D effect.
     */
    public static void drawEnhancedObstacle(Canvas canvas, Paint paint, float left, float top, 
                                           float right, float bottom, ObstacleType type) {
        // Save original paint color
        int originalColor = paint.getColor();
        
        // Draw the obstacle based on its type
        switch (type) {
            case BLOCK:
                // Create a gradient for 3D effect
                LinearGradient gradient = new LinearGradient(
                        left, top, 
                        right, bottom, 
                        new int[] {
                                Color.rgb(100, 100, 100), // Lighter gray
                                Color.rgb(70, 70, 70),    // Medium gray
                                Color.rgb(40, 40, 40)     // Darker gray
                        }, 
                        null, 
                        Shader.TileMode.CLAMP);
                
                paint.setShader(gradient);
                
                // Draw a solid block with gradient
                canvas.drawRect(left, top, right, bottom, paint);
                
                // Reset shader
                paint.setShader(null);
                
                // Add a highlight on top/left edges
                paint.setColor(Color.rgb(120, 120, 120));
                paint.setStrokeWidth(2);
                canvas.drawLine(left, top, right, top, paint);
                canvas.drawLine(left, top, left, bottom, paint);
                
                // Add a shadow on bottom/right edges
                paint.setColor(Color.rgb(30, 30, 30));
                canvas.drawLine(right, top, right, bottom, paint);
                canvas.drawLine(left, bottom, right, bottom, paint);
                
                // Add texture pattern
                paint.setColor(Color.argb(20, 0, 0, 0));
                float patternSize = (right - left) / 4;
                for (int i = 0; i < 4; i++) {
                    for (int j = 0; j < 4; j++) {
                        if ((i + j) % 2 == 0) {
                            canvas.drawRect(
                                    left + i * patternSize, 
                                    top + j * patternSize, 
                                    left + (i + 1) * patternSize, 
                                    top + (j + 1) * patternSize, 
                                    paint);
                        }
                    }
                }
                break;
        }
        
        // Restore original paint color
        paint.setColor(originalColor);
    }
    
    /**
     * Draws an enhanced game over screen.
     */
    public static void drawEnhancedGameOver(Canvas canvas, Paint paint, float screenWidth, float screenHeight, 
                                           int score, boolean isPortrait) {
        // Save original paint settings
        int originalColor = paint.getColor();
        float originalTextSize = paint.getTextSize();
        Paint.Align originalAlign = paint.getTextAlign();
        
        // Semi-transparent background
        paint.setColor(Color.argb(180, 0, 0, 0));
        canvas.drawRect(0, 0, screenWidth, screenHeight, paint);
        
        // Game over text
        float textSize = isPortrait ? 80 : 70;
        paint.setTextSize(textSize);
        paint.setColor(Color.RED);
        paint.setTextAlign(Paint.Align.CENTER);
        
        // Add shadow to text
        paint.setShadowLayer(10, 5, 5, Color.BLACK);
        canvas.drawText("GAME OVER", screenWidth / 2, screenHeight / 2 - 100, paint);
        paint.clearShadowLayer();
        
        // Score text
        paint.setTextSize(textSize * 0.6f);
        paint.setColor(Color.WHITE);
        canvas.drawText("Score: " + score, screenWidth / 2, screenHeight / 2, paint);
        
        // Draw restart button
        float buttonWidth = 200;
        float buttonHeight = 70;
        RectF restartButton = new RectF(
                screenWidth / 2 - buttonWidth / 2,
                screenHeight / 2 + 100 - buttonHeight / 2,
                screenWidth / 2 + buttonWidth / 2,
                screenHeight / 2 + 100 + buttonHeight / 2
        );
        
        // Button gradient
        LinearGradient buttonGradient = new LinearGradient(
                restartButton.left, restartButton.top,
                restartButton.left, restartButton.bottom,
                new int[] {
                        Color.rgb(0, 150, 0),  // Lighter green
                        Color.rgb(0, 100, 0)   // Darker green
                },
                null,
                Shader.TileMode.CLAMP);
        
        paint.setShader(buttonGradient);
        canvas.drawRoundRect(restartButton, 15, 15, paint);
        paint.setShader(null);
        
        // Button border
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(3);
        paint.setColor(Color.rgb(0, 200, 0));
        canvas.drawRoundRect(restartButton, 15, 15, paint);
        paint.setStyle(Paint.Style.FILL);
        
        // Button text
        paint.setTextSize(textSize * 0.4f);
        paint.setColor(Color.WHITE);
        canvas.drawText("RESTART", screenWidth / 2, screenHeight / 2 + 100 + textSize * 0.15f, paint);
        
        // Restore original paint settings
        paint.setColor(originalColor);
        paint.setTextSize(originalTextSize);
        paint.setTextAlign(originalAlign);
    }
    
    /**
     * Draws enhanced HUD elements.
     */
    public static void drawEnhancedHUD(Canvas canvas, Paint paint, float offsetX, float offsetY, 
                                      float gameWidth, float gameHeight, int score, boolean isPortrait) {
        // Save original paint settings
        int originalColor = paint.getColor();
        float originalTextSize = paint.getTextSize();
        Paint.Align originalAlign = paint.getTextAlign();
        
        // Draw semi-transparent panel for score
        RectF scorePanel = new RectF(offsetX, offsetY - 60, offsetX + 200, offsetY - 5);
        
        // Panel gradient
        LinearGradient panelGradient = new LinearGradient(
                scorePanel.left, scorePanel.top,
                scorePanel.left, scorePanel.bottom,
                new int[] {
                        Color.argb(180, 0, 50, 0),  // Darker green
                        Color.argb(180, 0, 100, 0)  // Lighter green
                },
                null,
                Shader.TileMode.CLAMP);
        
        paint.setShader(panelGradient);
        canvas.drawRoundRect(scorePanel, 10, 10, paint);
        paint.setShader(null);
        
        // Panel border
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(2);
        paint.setColor(Color.rgb(0, 150, 0));
        canvas.drawRoundRect(scorePanel, 10, 10, paint);
        paint.setStyle(Paint.Style.FILL);
        
        // Draw score
        paint.setTextSize(isPortrait ? 36 : 30);
        paint.setColor(Color.WHITE);
        paint.setShadowLayer(3, 1, 1, Color.BLACK);
        canvas.drawText("Score: " + score, offsetX + 15, offsetY - 20, paint);
        paint.clearShadowLayer();
        
        // Draw pause button
        float pauseButtonSize = 40;
        RectF pauseButton = new RectF(
                offsetX + gameWidth - pauseButtonSize - 10,
                offsetY - pauseButtonSize - 10,
                offsetX + gameWidth - 10,
                offsetY - 10
        );
        
        // Button gradient
        LinearGradient buttonGradient = new LinearGradient(
                pauseButton.left, pauseButton.top,
                pauseButton.left, pauseButton.bottom,
                new int[] {
                        Color.argb(180, 100, 100, 100),  // Lighter gray
                        Color.argb(180, 50, 50, 50)      // Darker gray
                },
                null,
                Shader.TileMode.CLAMP);
        
        paint.setShader(buttonGradient);
        canvas.drawRoundRect(pauseButton, 5, 5, paint);
        paint.setShader(null);
        
        // Button border
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(2);
        paint.setColor(Color.rgb(150, 150, 150));
        canvas.drawRoundRect(pauseButton, 5, 5, paint);
        paint.setStyle(Paint.Style.FILL);
        
        // Draw pause icon
        paint.setColor(Color.WHITE);
        float pauseIconSize = pauseButtonSize * 0.4f;
        float centerX = pauseButton.centerX();
        float centerY = pauseButton.centerY();
        canvas.drawRect(centerX - pauseIconSize, centerY - pauseIconSize,
                       centerX - pauseIconSize * 0.3f, centerY + pauseIconSize, paint);
        canvas.drawRect(centerX + pauseIconSize * 0.3f, centerY - pauseIconSize,
                       centerX + pauseIconSize, centerY + pauseIconSize, paint);
        
        // Restore original paint settings
        paint.setColor(originalColor);
        paint.setTextSize(originalTextSize);
        paint.setTextAlign(originalAlign);
    }
    
    /**
     * Creates a particle effect for snake eating food.
     */
    public static void drawFoodParticles(Canvas canvas, Paint paint, float centerX, float centerY, 
                                        float radius, int color, float progress) {
        // Save original paint color
        int originalColor = paint.getColor();
        
        // Draw particles
        int numParticles = 12;
        float maxDistance = radius * 3 * progress;
        
        for (int i = 0; i < numParticles; i++) {
            float angle = (float) (i * (2 * Math.PI / numParticles));
            float distance = maxDistance * (0.5f + 0.5f * (float)Math.sin(progress * Math.PI));
            
            float x = centerX + distance * (float)Math.cos(angle);
            float y = centerY + distance * (float)Math.sin(angle);
            
            float particleSize = radius * 0.3f * (1 - progress);
            
            // Fade out as progress increases
            int alpha = (int)(255 * (1 - progress));
            paint.setColor(Color.argb(alpha, Color.red(color), Color.green(color), Color.blue(color)));
            
            canvas.drawCircle(x, y, particleSize, paint);
        }
        
        // Restore original paint color
        paint.setColor(originalColor);
    }
    
    /**
     * Creates a particle effect for snake collision.
     */
    public static void drawCollisionParticles(Canvas canvas, Paint paint, float centerX, float centerY, 
                                             float radius, int color, float progress) {
        // Save original paint color
        int originalColor = paint.getColor();
        
        // Draw explosion particles
        int numParticles = 20;
        float maxDistance = radius * 4 * progress;
        
        for (int i = 0; i < numParticles; i++) {
            float angle = (float) (i * (2 * Math.PI / numParticles) + progress * Math.PI);
            float distance = maxDistance;
            
            float x = centerX + distance * (float)Math.cos(angle);
            float y = centerY + distance * (float)Math.sin(angle);
            
            float particleSize = radius * 0.4f * (1 - progress);
            
            // Fade out as progress increases
            int alpha = (int)(255 * (1 - progress));
            paint.setColor(Color.argb(alpha, 255, 255, 255)); // White particles
            
            canvas.drawCircle(x, y, particleSize, paint);
        }
        
        // Draw shock wave
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(radius * 0.2f * (1 - progress));
        
        int alpha = (int)(100 * (1 - progress));
        paint.setColor(Color.argb(alpha, 255, 255, 255));
        
        canvas.drawCircle(centerX, centerY, radius * 5 * progress, paint);
        
        paint.setStyle(Paint.Style.FILL);
        
        // Restore original paint color
        paint.setColor(originalColor);
    }
    
    /**
     * Lightens a color by the given factor.
     */
    public static int lightenColor(int color, float factor) {
        int r = Math.min(255, (int)(Color.red(color) + (255 - Color.red(color)) * factor));
        int g = Math.min(255, (int)(Color.green(color) + (255 - Color.green(color)) * factor));
        int b = Math.min(255, (int)(Color.blue(color) + (255 - Color.blue(color)) * factor));
        return Color.rgb(r, g, b);
    }
    
    /**
     * Lightens a color by a default factor of 0.3.
     */
    public static int lightenColor(int color) {
        return lightenColor(color, 0.3f);
    }
    
    /**
     * Darkens a color by the given factor.
     */
    public static int darkenColor(int color, float factor) {
        int r = (int)(Color.red(color) * (1 - factor));
        int g = (int)(Color.green(color) * (1 - factor));
        int b = (int)(Color.blue(color) * (1 - factor));
        return Color.rgb(r, g, b);
    }
    
    /**
     * Interpolates between two colors.
     */
    public static int interpolateColor(int color1, int color2, float ratio) {
        int r = (int)(Color.red(color1) * (1 - ratio) + Color.red(color2) * ratio);
        int g = (int)(Color.green(color1) * (1 - ratio) + Color.green(color2) * ratio);
        int b = (int)(Color.blue(color1) * (1 - ratio) + Color.blue(color2) * ratio);
        return Color.rgb(r, g, b);
    }
}
