package com.example.snakeclash.game;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;

import java.util.List;

/**
 * Handles rendering of the game.
 */
public class GameRenderer {
    // Rendering
    private Paint paint;
    private Paint gridPaint;
    private Paint hudPaint;
    private Paint instructionsPaint;
    private int screenWidth;
    private int screenHeight;
    private float cellSize;
    
    // UI elements
    private RectF pauseButton;
    private boolean showInstructions;
    private float instructionsAlpha;
    private static final float INSTRUCTIONS_FADE_SPEED = 0.5f; // Alpha units per second
    
    // Power-up notification
    private boolean showPowerUpNotification;
    private String powerUpNotificationMessage;
    private float powerUpNotificationAlpha;
    private static final float NOTIFICATION_FADE_SPEED = 0.3f; // Alpha units per second
    private static final float NOTIFICATION_DURATION = 3.0f; // seconds
    
    // Game engine reference
    private GameEngine gameEngine;
    
    /**
     * Constructor.
     */
    public GameRenderer(GameEngine gameEngine) {
        this.gameEngine = gameEngine;
        
        // Initialize main paint
        paint = new Paint();
        paint.setAntiAlias(true);
        
        // Initialize grid paint
        gridPaint = new Paint();
        gridPaint.setColor(Color.argb(30, 255, 255, 255)); // Very light grid
        gridPaint.setStrokeWidth(1);
        gridPaint.setStyle(Paint.Style.STROKE);
        
        // Initialize HUD paint
        hudPaint = new Paint();
        hudPaint.setAntiAlias(true);
        hudPaint.setTextSize(40);
        hudPaint.setColor(Color.WHITE);
        hudPaint.setTextAlign(Paint.Align.LEFT);
        
        // Initialize instructions paint
        instructionsPaint = new Paint();
        instructionsPaint.setAntiAlias(true);
        instructionsPaint.setTextSize(30);
        instructionsPaint.setColor(Color.WHITE);
        instructionsPaint.setTextAlign(Paint.Align.CENTER);
        
        // Initialize UI elements
        pauseButton = new RectF();
        showInstructions = true;
        instructionsAlpha = 1.0f;
        
        // Initialize notification
        showPowerUpNotification = false;
        powerUpNotificationMessage = "";
        powerUpNotificationAlpha = 0.0f;
    }
    
    /**
     * Updates the renderer state.
     */
    public void update(float deltaTime) {
        // Update instructions fade-out
        if (showInstructions) {
            instructionsAlpha -= INSTRUCTIONS_FADE_SPEED * deltaTime;
            if (instructionsAlpha <= 0) {
                instructionsAlpha = 0;
                showInstructions = false;
            }
        }
        
        // Update power-up notification
        if (showPowerUpNotification) {
            powerUpNotificationAlpha -= NOTIFICATION_FADE_SPEED * deltaTime;
            if (powerUpNotificationAlpha <= 0) {
                powerUpNotificationAlpha = 0;
                showPowerUpNotification = false;
            }
        }
    }
    
    /**
     * Renders the game.
     */
    public void render(Canvas canvas) {
        if (canvas == null) return;
        
        // Update screen dimensions
        screenWidth = canvas.getWidth();
        screenHeight = canvas.getHeight();
        
        // Clear the canvas with a dark green background
        canvas.drawColor(Color.rgb(0, 20, 0));
        
        // Get effective grid size
        int[] gridSizes = gameEngine.getEffectiveGridSize();
        int effectiveGridWidth = gridSizes[0];
        int effectiveGridHeight = gridSizes[1];
        boolean isPortrait = screenHeight > screenWidth;
        
        // Calculate cell size to use maximum screen area
        float horizontalCellSize = screenWidth / (float) effectiveGridWidth;
        float verticalCellSize = screenHeight / (float) effectiveGridHeight;
        cellSize = Math.min(horizontalCellSize, verticalCellSize);
        
        // Calculate game area bounds to fill maximum space
        float gameWidth = cellSize * effectiveGridWidth;
        float gameHeight = cellSize * effectiveGridHeight;
        float offsetX = (screenWidth - gameWidth) / 2;
        float offsetY = (screenHeight - gameHeight) / 2;
        
        // Draw grid pattern
        drawGrid(canvas, offsetX, offsetY, gameWidth, gameHeight, effectiveGridWidth, effectiveGridHeight);
        
        // Draw boundary
        paint.setColor(Color.rgb(100, 255, 100)); // Bright green border
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(3);
        canvas.drawRect(offsetX, offsetY, offsetX + gameWidth, offsetY + gameHeight, paint);
        paint.setStyle(Paint.Style.FILL);
        
        // Render obstacles (draw these first as they're in the background)
        for (Obstacle obstacle : gameEngine.getObstacles()) {
            obstacle.render(canvas, paint, cellSize, offsetX, offsetY);
        }
        
        // Render food
        for (Food food : gameEngine.getFoods()) {
            food.render(canvas, paint, cellSize, offsetX, offsetY);
        }
        
        // Render power-ups
        for (PowerUp powerUp : gameEngine.getPowerUps()) {
            if (!powerUp.isActive()) { // Only render inactive (collectible) power-ups
                powerUp.render(canvas, paint, cellSize, offsetX, offsetY);
            }
        }
        
        // Render snakes
        for (Snake snake : gameEngine.getSnakes()) {
            snake.render(canvas, paint, cellSize, offsetX, offsetY);
        }
        
        // Draw HUD elements
        drawHUD(canvas, offsetX, offsetY, gameWidth, gameHeight, isPortrait);
        
        // Draw instructions if needed
        if (showInstructions) {
            drawInstructions(canvas);
        }
        
        // Draw power-up notification if needed
        if (showPowerUpNotification) {
            drawPowerUpNotification(canvas);
        }
        
        // Render game over message if needed
        if (gameEngine.isGameOver()) {
            drawGameOver(canvas, isPortrait);
        }
    }
    
    /**
     * Draws the grid pattern in the background.
     */
    private void drawGrid(Canvas canvas, float offsetX, float offsetY, float gameWidth, float gameHeight, 
                          int gridWidth, int gridHeight) {
        // Draw vertical grid lines
        for (int i = 0; i <= gridWidth; i++) {
            float x = offsetX + i * cellSize;
            canvas.drawLine(x, offsetY, x, offsetY + gameHeight, gridPaint);
        }
        
        // Draw horizontal grid lines
        for (int i = 0; i <= gridHeight; i++) {
            float y = offsetY + i * cellSize;
            canvas.drawLine(offsetX, y, offsetX + gameWidth, y, gridPaint);
        }
    }
    
    /**
     * Draws the HUD elements (score, level, pause button, and power-up timer).
     */
    private void drawHUD(Canvas canvas, float offsetX, float offsetY, float gameWidth, float gameHeight, boolean isPortrait) {
        // Draw semi-transparent panel for score
        paint.setColor(Color.argb(100, 0, 0, 0));
        RectF scorePanel = new RectF(offsetX, offsetY - 60, offsetX + 200, offsetY - 5);
        canvas.drawRoundRect(scorePanel, 10, 10, paint);
        
        // Draw score
        hudPaint.setTextSize(isPortrait ? 36 : 30);
        canvas.drawText("Score: " + gameEngine.getScore(), offsetX + 15, offsetY - 20, hudPaint);
        
        // Draw level indicator on the right side
        paint.setColor(Color.argb(100, 0, 0, 0));
        RectF levelPanel = new RectF(offsetX + gameWidth - 120, offsetY - 60, offsetX + gameWidth, offsetY - 5);
        canvas.drawRoundRect(levelPanel, 10, 10, paint);
        
        hudPaint.setTextAlign(Paint.Align.CENTER);
        canvas.drawText("Level 1", offsetX + gameWidth - 60, offsetY - 20, hudPaint);
        hudPaint.setTextAlign(Paint.Align.LEFT);
        
        // Draw pause button
        paint.setColor(Color.argb(100, 0, 0, 0));
        pauseButton.set(offsetX + gameWidth + 10, offsetY, offsetX + gameWidth + 60, offsetY + 50);
        canvas.drawRoundRect(pauseButton, 10, 10, paint);
        
        // Draw pause icon
        paint.setColor(Color.WHITE);
        paint.setStrokeWidth(5);
        float pauseX = pauseButton.centerX();
        float pauseY = pauseButton.centerY();
        canvas.drawLine(pauseX - 7, pauseY - 10, pauseX - 7, pauseY + 10, paint);
        canvas.drawLine(pauseX + 7, pauseY - 10, pauseX + 7, pauseY + 10, paint);
        
        // Draw power-up timer if active
        drawPowerUpTimer(canvas, offsetX, offsetY, gameWidth);
    }
    
    /**
     * Draws the power-up timer if any power-up is active.
     */
    private void drawPowerUpTimer(Canvas canvas, float offsetX, float offsetY, float gameWidth) {
        // Check if any snake has an active power-up
        boolean hasActivePowerUp = false;
        float remainingTime = 0;
        
        // Find active power-ups
        for (PowerUp powerUp : gameEngine.getPowerUps()) {
            if (powerUp.isActive()) {
                hasActivePowerUp = true;
                remainingTime = PowerUp.ACTIVE_DURATION - powerUp.getActiveTime();
                break;
            }
        }
        
        if (hasActivePowerUp) {
            // Draw timer panel in the center top
            float panelWidth = 180;
            float panelHeight = 50;
            float panelX = offsetX + (gameWidth - panelWidth) / 2;
            
            // Draw background panel
            paint.setColor(Color.argb(180, 0, 100, 150)); // Cyan-blue background
            RectF timerPanel = new RectF(panelX, offsetY - 60, panelX + panelWidth, offsetY - 5);
            canvas.drawRoundRect(timerPanel, 10, 10, paint);
            
            // Draw lightning bolt icon
            paint.setColor(Color.CYAN);
            float iconX = panelX + 25;
            float iconY = offsetY - 32;
            float iconSize = 15;
            
            Path path = new Path();
            path.moveTo(iconX, iconY - iconSize);
            path.lineTo(iconX - iconSize/2, iconY);
            path.lineTo(iconX, iconY - iconSize/3);
            path.lineTo(iconX, iconY + iconSize);
            path.lineTo(iconX + iconSize/2, iconY);
            path.lineTo(iconX, iconY + iconSize/3);
            path.close();
            canvas.drawPath(path, paint);
            
            // Draw countdown text
            hudPaint.setTextSize(30);
            hudPaint.setColor(Color.WHITE);
            hudPaint.setTextAlign(Paint.Align.LEFT);
            String timeText = "Speed: " + (int)Math.ceil(remainingTime) + "s";
            canvas.drawText(timeText, panelX + 50, offsetY - 20, hudPaint);
            
            // Draw progress bar
            float progress = remainingTime / PowerUp.ACTIVE_DURATION;
            float barWidth = (panelWidth - 20) * progress;
            
            // Background bar
            paint.setColor(Color.argb(100, 255, 255, 255));
            RectF barBg = new RectF(panelX + 10, offsetY - 15, panelX + panelWidth - 10, offsetY - 10);
            canvas.drawRect(barBg, paint);
            
            // Progress bar
            paint.setColor(Color.WHITE);
            RectF bar = new RectF(panelX + 10, offsetY - 15, panelX + 10 + barWidth, offsetY - 10);
            canvas.drawRect(bar, paint);
        }
    }
    
    /**
     * Draws the instructions overlay.
     */
    private void drawInstructions(Canvas canvas) {
        // Set alpha for fading effect
        instructionsPaint.setAlpha((int)(instructionsAlpha * 255));
        
        // Draw semi-transparent background
        paint.setColor(Color.argb((int)(instructionsAlpha * 128), 0, 0, 0));
        canvas.drawRect(0, screenHeight / 2 - 100, screenWidth, screenHeight / 2 + 100, paint);
        
        // Draw instructions text
        canvas.drawText("Level 1: Use swipe gestures to navigate", screenWidth / 2, screenHeight / 2 - 50, instructionsPaint);
        canvas.drawText("Eat food to grow, avoid walls and other snakes", screenWidth / 2, screenHeight / 2, instructionsPaint);
        canvas.drawText("Bigger snakes can eat smaller ones!", screenWidth / 2, screenHeight / 2 + 50, instructionsPaint);
    }
    
    /**
     * Draws the power-up notification.
     */
    private void drawPowerUpNotification(Canvas canvas) {
        // Set up paint
        Paint notificationPaint = new Paint(instructionsPaint);
        notificationPaint.setTextSize(40);
        notificationPaint.setColor(Color.CYAN);
        notificationPaint.setAlpha((int)(powerUpNotificationAlpha * 255));
        
        // Draw semi-transparent background
        paint.setColor(Color.argb((int)(powerUpNotificationAlpha * 100), 0, 0, 50)); // Dark blue tint
        RectF notificationBg = new RectF(0, 50, screenWidth, 150);
        canvas.drawRoundRect(notificationBg, 20, 20, paint);
        
        // Draw lightning bolt icon
        paint.setColor(Color.CYAN);
        paint.setAlpha((int)(powerUpNotificationAlpha * 255));
        
        // Draw a small lightning bolt
        Path path = new Path();
        float centerX = 80;
        float centerY = 100;
        float size = 30;
        
        path.moveTo(centerX, centerY - size);
        path.lineTo(centerX - size/2, centerY);
        path.lineTo(centerX, centerY - size/3);
        path.lineTo(centerX, centerY + size);
        path.lineTo(centerX + size/2, centerY);
        path.lineTo(centerX, centerY + size/3);
        path.close();
        
        canvas.drawPath(path, paint);
        
        // Draw notification text
        canvas.drawText(powerUpNotificationMessage, screenWidth / 2, 100, notificationPaint);
    }
    
    /**
     * Draws the game over screen.
     */
    private void drawGameOver(Canvas canvas, boolean isPortrait) {
        // Draw semi-transparent overlay
        paint.setColor(Color.argb(180, 0, 0, 0)); // More opaque black
        canvas.drawRect(0, 0, screenWidth, screenHeight, paint);
        
        // Draw game over panel
        paint.setColor(Color.argb(200, 50, 0, 0)); // Dark red panel
        float panelWidth = Math.min(screenWidth * 0.8f, 500);
        float panelHeight = 300;
        RectF gameOverPanel = new RectF(
            screenWidth / 2 - panelWidth / 2,
            screenHeight / 2 - panelHeight / 2,
            screenWidth / 2 + panelWidth / 2,
            screenHeight / 2 + panelHeight / 2
        );
        canvas.drawRoundRect(gameOverPanel, 20, 20, paint);
        
        // Draw border
        paint.setStyle(Paint.Style.STROKE);
        paint.setColor(Color.RED);
        paint.setStrokeWidth(5);
        canvas.drawRoundRect(gameOverPanel, 20, 20, paint);
        paint.setStyle(Paint.Style.FILL);
        
        // Draw game over text
        float textSize = isPortrait ? 60 : 50;
        float yPosition = screenHeight / 2 - 50;
        float spacing = isPortrait ? 70 : 60;
        
        paint.setColor(Color.RED);
        paint.setTextSize(textSize);
        paint.setTextAlign(Paint.Align.CENTER);
        canvas.drawText("GAME OVER!", screenWidth / 2, yPosition, paint);
        
        // Draw final score
        paint.setColor(Color.WHITE);
        paint.setTextSize(textSize * 0.5f);
        canvas.drawText("Final Score: " + gameEngine.getScore(), screenWidth / 2, yPosition + spacing, paint);
        
        // Draw restart button
        paint.setColor(Color.rgb(0, 100, 0)); // Dark green
        RectF restartButton = new RectF(
            screenWidth / 2 - 100,
            yPosition + spacing * 2 - 25,
            screenWidth / 2 + 100,
            yPosition + spacing * 2 + 25
        );
        canvas.drawRoundRect(restartButton, 15, 15, paint);
        
        // Draw restart text
        paint.setColor(Color.WHITE);
        paint.setTextSize(textSize * 0.4f);
        canvas.drawText("RESTART", screenWidth / 2, yPosition + spacing * 2 + 15, paint);
    }
    
    /**
     * Shows a notification about a power-up spawning.
     */
    public void showPowerUpNotification(String message) {
        powerUpNotificationMessage = message;
        showPowerUpNotification = true;
        powerUpNotificationAlpha = 1.0f;
    }
    
    /**
     * Gets the pause button.
     */
    public RectF getPauseButton() {
        return pauseButton;
    }
    
    /**
     * Sets whether to show instructions.
     */
    public void setShowInstructions(boolean showInstructions) {
        this.showInstructions = showInstructions;
        if (showInstructions) {
            instructionsAlpha = 1.0f;
        }
    }
    
    /**
     * Gets the screen width.
     */
    public int getScreenWidth() {
        return screenWidth;
    }
    
    /**
     * Gets the screen height.
     */
    public int getScreenHeight() {
        return screenHeight;
    }
    
    /**
     * Gets the cell size.
     */
    public float getCellSize() {
        return cellSize;
    }
}
