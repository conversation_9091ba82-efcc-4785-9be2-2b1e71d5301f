package com.example.snakeclash.game;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Represents a snake.
 */
public class Snake {
    private final int id;
    private final int color;
    private final boolean isAI;
    private final List<Point> body;
    private Direction direction;
    private float moveTimer;
    private float moveInterval;
    private float normalMoveInterval; // Store the normal speed
    private float collisionCooldown;
    private float lastCollisionTime;
    private boolean hasSpeedBoost;
    private static final float COLLISION_COOLDOWN_TIME = 0.5f;
    private static final float COLLISION_FLASH_DURATION = 0.3f;

    public Snake(int id, int x, int y, int color, boolean isAI) {
        this.id = id;
        this.color = color;
        this.isAI = isAI;
        this.body = new ArrayList<>();
        this.direction = Direction.values()[new Random().nextInt(Direction.values().length)];
        this.moveTimer = 0;
        this.moveInterval = isAI ? 0.3f : 0.2f; // AI moves slower
        this.normalMoveInterval = this.moveInterval; // Store normal speed
        this.collisionCooldown = 0;
        this.hasSpeedBoost = false;

        // Add initial segments
        body.add(new Point(x, y)); // Head
        addSegments(isAI ? 3 : 5); // Player starts with more segments
    }

    public void update(float deltaTime, List<Food> foods, List<Snake> snakes, List<Obstacle> obstacles) {
        // Update collision cooldown
        if (collisionCooldown > 0) {
            collisionCooldown -= deltaTime;
        }

        // Update move timer
        moveTimer += deltaTime;
        if (moveTimer >= moveInterval) {
            moveTimer = 0;

            // Move the snake
            move();

            // Check for obstacle collision
            if (checkObstacleCollision(obstacles)) {
                // If we hit an obstacle, don't check for other collisions
                return;
            }

            // Check for food collision
            checkFoodCollision(foods);

            // Check for snake collision
            if (collisionCooldown <= 0) {
                checkSnakeCollision(snakes);
            }

            // AI behavior
            if (isAI) {
                updateAI(foods, snakes, obstacles);
            }
        }
    }

    private void move() {
        if (body.isEmpty()) return;

        // Get the head
        Point head = body.get(0);

        // Calculate new head position
        int newX = head.x;
        int newY = head.y;

        switch (direction) {
            case UP:
                newY--;
                break;
            case DOWN:
                newY++;
                break;
            case LEFT:
                newX--;
                break;
            case RIGHT:
                newX++;
                break;
        }

        // Get effective grid size
        int effectiveGridWidth = 30; // This would be dynamic in the real implementation
        int effectiveGridHeight = 30; // This would be dynamic in the real implementation

        // Wrap around grid boundaries
        newX = (newX + effectiveGridWidth) % effectiveGridWidth;
        newY = (newY + effectiveGridHeight) % effectiveGridHeight;

        // Add new head
        Point newHead = new Point(newX, newY);
        body.add(0, newHead);

        // Remove tail if not growing
        if (body.size() > 1) {
            body.remove(body.size() - 1);
        }
    }

    /**
     * Checks for collision with obstacles.
     * @return true if a collision occurred
     */
    private boolean checkObstacleCollision(List<Obstacle> obstacles) {
        if (body.isEmpty()) return false;

        Point head = body.get(0);

        // Check if the head collided with any obstacle
        for (Obstacle obstacle : obstacles) {
            if (head.x == obstacle.getX() && head.y == obstacle.getY()) {
                // We hit an obstacle - bounce off
                bounceOffObstacle(obstacle);
                return true;
            }
        }

        return false;
    }

    /**
     * Handles bouncing off an obstacle.
     */
    private void bounceOffObstacle(Obstacle obstacle) {
        // Reverse direction
        switch (direction) {
            case UP:
                direction = Direction.DOWN;
                break;
            case DOWN:
                direction = Direction.UP;
                break;
            case LEFT:
                direction = Direction.RIGHT;
                break;
            case RIGHT:
                direction = Direction.LEFT;
                break;
        }

        // Move the head away from the obstacle to prevent getting stuck
        if (!body.isEmpty()) {
            Point head = body.get(0);

            // Remove the head
            body.remove(0);

            // Add a new head in the opposite direction
            Point newHead = null;
            switch (direction) {
                case UP:
                    newHead = new Point(head.x, head.y - 1);
                    break;
                case DOWN:
                    newHead = new Point(head.x, head.y + 1);
                    break;
                case LEFT:
                    newHead = new Point(head.x - 1, head.y);
                    break;
                case RIGHT:
                    newHead = new Point(head.x + 1, head.y);
                    break;
            }

            // Get effective grid size
            int effectiveGridWidth = 30; // This would be dynamic in the real implementation
            int effectiveGridHeight = 30; // This would be dynamic in the real implementation

            // Wrap around grid boundaries
            if (newHead != null) {
                newHead.x = (newHead.x + effectiveGridWidth) % effectiveGridWidth;
                newHead.y = (newHead.y + effectiveGridHeight) % effectiveGridHeight;
                body.add(0, newHead);
            }
        }

        // Set collision cooldown
        collisionCooldown = COLLISION_COOLDOWN_TIME;

        // Visual feedback
        lastCollisionTime = moveTimer;
    }

    private void checkFoodCollision(List<Food> foods) {
        if (body.isEmpty()) return;

        Point head = body.get(0);

        for (int i = 0; i < foods.size(); i++) {
            Food food = foods.get(i);

            if (head.x == food.getX() && head.y == food.getY()) {
                // Eat the food
                foods.remove(i);

                // Grow the snake
                addSegment();

                // Increase speed slightly
                moveInterval = Math.max(0.1f, moveInterval - 0.01f);

                break;
            }
        }
    }

    private void checkSnakeCollision(List<Snake> snakes) {
        if (body.isEmpty()) return;

        // Skip if we're in cooldown
        if (collisionCooldown > 0) return;

        Point head = body.get(0);

        for (Snake otherSnake : snakes) {
            // Skip self
            if (otherSnake.id == this.id) continue;

            // Check collision with other snake's body
            for (int i = 0; i < otherSnake.body.size(); i++) {
                Point otherSegment = otherSnake.body.get(i);

                if (head.x == otherSegment.x && head.y == otherSegment.y) {
                    // Handle collision based on size
                    handleSnakeCollision(otherSnake, i);
                    return; // Only handle one collision per update
                }
            }
        }
    }

    private void handleSnakeCollision(Snake otherSnake, int segmentIndex) {
        // Compare sizes
        if (body.size() > otherSnake.body.size()) {
            // We're bigger - we can eat the other snake
            if (segmentIndex == 0) {
                // We hit the head - eat the whole snake
                int segmentsToAdd = otherSnake.body.size();
                otherSnake.die();
                addSegments(segmentsToAdd);
            } else {
                // We hit the body - eat part of the snake
                int segmentsToAdd = otherSnake.body.size() - segmentIndex;
                otherSnake.removeSegmentsFrom(segmentIndex);
                addSegments(segmentsToAdd);
            }
        } else if (segmentIndex == 0 && body.size() < otherSnake.body.size()) {
            // We're smaller and hit the head of a bigger snake - get eaten completely
            int segmentsToAdd = body.size();
            die();
            otherSnake.addSegments(segmentsToAdd);
        } else {
            // We're equal size or hit the body of a bigger snake - bounce off
            bounceOff(otherSnake, segmentIndex);
        }
    }

    private void bounceOff(Snake otherSnake, int segmentIndex) {
        // Get our head and the other snake's segment we collided with
        Point head = body.get(0);
        Point otherSegment = otherSnake.body.get(segmentIndex);

        // Determine bounce direction based on collision point
        if (segmentIndex == 0) {
            // Head-to-head collision - bounce in opposite direction
            switch (direction) {
                case UP:
                    direction = Direction.DOWN;
                    break;
                case DOWN:
                    direction = Direction.UP;
                    break;
                case LEFT:
                    direction = Direction.RIGHT;
                    break;
                case RIGHT:
                    direction = Direction.LEFT;
                    break;
            }
        } else {
            // We hit the other snake's body - bounce based on relative position
            // Try to find a direction away from the collision

            // Get the previous segment in the other snake to determine its body direction
            Point otherPrevSegment = null;
            if (segmentIndex < otherSnake.body.size() - 1) {
                otherPrevSegment = otherSnake.body.get(segmentIndex + 1);
            }

            // Determine the best direction to bounce
            if (otherPrevSegment != null) {
                // If other snake is horizontal (left/right)
                if (otherPrevSegment.y == otherSegment.y) {
                    // Bounce vertically
                    if (head.y < otherSegment.y) {
                        direction = Direction.UP;
                    } else {
                        direction = Direction.DOWN;
                    }
                }
                // If other snake is vertical (up/down)
                else if (otherPrevSegment.x == otherSegment.x) {
                    // Bounce horizontally
                    if (head.x < otherSegment.x) {
                        direction = Direction.LEFT;
                    } else {
                        direction = Direction.RIGHT;
                    }
                }
                // If diagonal or can't determine, just reverse direction
                else {
                    switch (direction) {
                        case UP:
                            direction = Direction.DOWN;
                            break;
                        case DOWN:
                            direction = Direction.UP;
                            break;
                        case LEFT:
                            direction = Direction.RIGHT;
                            break;
                        case RIGHT:
                            direction = Direction.LEFT;
                            break;
                    }
                }
            } else {
                // Can't determine other snake's direction, just reverse
                switch (direction) {
                    case UP:
                        direction = Direction.DOWN;
                        break;
                    case DOWN:
                        direction = Direction.UP;
                        break;
                    case LEFT:
                        direction = Direction.RIGHT;
                        break;
                    case RIGHT:
                        direction = Direction.LEFT;
                        break;
                }
            }
        }

        // Set collision cooldown
        collisionCooldown = COLLISION_COOLDOWN_TIME;

        // Visual feedback for bounce - change color briefly
        // We'll implement this by adding a flash effect in the render method
        lastCollisionTime = moveTimer;
    }

    private void updateAI(List<Food> foods, List<Snake> snakes, List<Obstacle> obstacles) {
        // Simple AI: find nearest food or smaller snake to chase
        if (body.isEmpty()) return;

        Point head = body.get(0);

        // Occasionally change direction randomly
        if (new Random().nextFloat() < 0.1f) {
            direction = Direction.values()[new Random().nextInt(Direction.values().length)];
            return;
        }

        // Target coordinates
        int targetX = head.x;
        int targetY = head.y;
        int minDistance = Integer.MAX_VALUE;

        // Check for nearby food
        for (Food food : foods) {
            int distance = Math.abs(head.x - food.getX()) + Math.abs(head.y - food.getY());
            if (distance < minDistance) {
                minDistance = distance;
                targetX = food.getX();
                targetY = food.getY();
            }
        }

        // Check for smaller snakes to chase
        for (Snake otherSnake : snakes) {
            if (otherSnake.id != this.id && otherSnake.body.size() < this.body.size()) {
                Point otherHead = otherSnake.getHead();
                int distance = Math.abs(head.x - otherHead.x) + Math.abs(head.y - otherHead.y);
                if (distance < minDistance) {
                    minDistance = distance;
                    targetX = otherHead.x;
                    targetY = otherHead.y;
                }
            }
        }

        // Check for obstacles to avoid
        Direction currentDir = direction;
        Direction[] possibleDirs = new Direction[4];
        int numDirs = 0;

        // Consider all possible directions
        for (Direction dir : Direction.values()) {
            // Skip the opposite of current direction
            if ((currentDir == Direction.UP && dir == Direction.DOWN) ||
                (currentDir == Direction.DOWN && dir == Direction.UP) ||
                (currentDir == Direction.LEFT && dir == Direction.RIGHT) ||
                (currentDir == Direction.RIGHT && dir == Direction.LEFT)) {
                continue;
            }

            // Check if moving in this direction would hit an obstacle
            Point nextPos = new Point(head.x, head.y);
            switch (dir) {
                case UP:
                    nextPos.y--;
                    break;
                case DOWN:
                    nextPos.y++;
                    break;
                case LEFT:
                    nextPos.x--;
                    break;
                case RIGHT:
                    nextPos.x++;
                    break;
            }

            // Wrap around grid boundaries
            int effectiveGridWidth = 30; // This would be dynamic in the real implementation
            int effectiveGridHeight = 30; // This would be dynamic in the real implementation
            nextPos.x = (nextPos.x + effectiveGridWidth) % effectiveGridWidth;
            nextPos.y = (nextPos.y + effectiveGridHeight) % effectiveGridHeight;

            // Check if this position has an obstacle
            boolean hasObstacle = false;
            for (Obstacle obstacle : obstacles) {
                if (nextPos.x == obstacle.getX() && nextPos.y == obstacle.getY()) {
                    hasObstacle = true;
                    break;
                }
            }

            if (!hasObstacle) {
                possibleDirs[numDirs++] = dir;
            }
        }

        // If we have valid directions, choose the one closest to target
        if (numDirs > 0) {
            // Default to current direction if it's valid
            Direction bestDir = currentDir;
            int bestDistance = Integer.MAX_VALUE;

            for (int i = 0; i < numDirs; i++) {
                Direction dir = possibleDirs[i];
                Point nextPos = new Point(head.x, head.y);

                switch (dir) {
                    case UP:
                        nextPos.y--;
                        break;
                    case DOWN:
                        nextPos.y++;
                        break;
                    case LEFT:
                        nextPos.x--;
                        break;
                    case RIGHT:
                        nextPos.x++;
                        break;
                }

                // Calculate distance to target
                int distance = Math.abs(nextPos.x - targetX) + Math.abs(nextPos.y - targetY);
                if (distance < bestDistance) {
                    bestDistance = distance;
                    bestDir = dir;
                }
            }

            direction = bestDir;
        } else {
            // All directions have obstacles, just use the basic targeting
            if (Math.abs(head.x - targetX) > Math.abs(head.y - targetY)) {
                // Move horizontally
                if (head.x < targetX) {
                    direction = Direction.RIGHT;
                } else if (head.x > targetX) {
                    direction = Direction.LEFT;
                }
            } else {
                // Move vertically
                if (head.y < targetY) {
                    direction = Direction.DOWN;
                } else if (head.y > targetY) {
                    direction = Direction.UP;
                }
            }
        }
    }

    public void render(Canvas canvas, Paint paint, float cellSize, float offsetX, float offsetY) {
        // Check if we should show the bounce flash effect
        boolean showFlash = (moveTimer - lastCollisionTime) < COLLISION_FLASH_DURATION;

        // Draw body segments from tail to head
        for (int i = body.size() - 1; i >= 0; i--) {
            Point segment = body.get(i);

            float centerX = offsetX + segment.x * cellSize + cellSize / 2;
            float centerY = offsetY + segment.y * cellSize + cellSize / 2;

            if (i == 0) {
                // This is the head - draw it with enhanced graphics
                float headRadius = cellSize * 0.45f;
                GraphicsUtils.drawEnhancedSnakeSegment(canvas, paint, centerX, centerY,
                                                     headRadius, color, true, hasSpeedBoost,
                                                     showFlash, direction);
            } else {
                // This is a body segment - draw with enhanced graphics
                float radius = cellSize * 0.4f;

                // Determine segment color based on position and effects
                int segmentColor = color;
                if (showFlash && i < 3) {
                    // Flash white with fading effect based on segment position
                    float flashIntensity = 1.0f - (i / 3.0f);
                    segmentColor = GraphicsUtils.interpolateColor(color, Color.WHITE, flashIntensity);
                } else if (hasSpeedBoost) {
                    // Add speed effect (cyan trail)
                    float speedEffectIntensity = Math.max(0, 1.0f - (i / (float)body.size()));
                    segmentColor = GraphicsUtils.interpolateColor(color, Color.CYAN, speedEffectIntensity * 0.3f);
                }

                GraphicsUtils.drawEnhancedSnakeSegment(canvas, paint, centerX, centerY,
                                                     radius, segmentColor, false, hasSpeedBoost,
                                                     showFlash && i < 3, direction);
            }
        }

        // Draw speed boost effect
        if (hasSpeedBoost && !body.isEmpty()) {
            // Draw a trail behind the snake
            Point tail = body.get(body.size() - 1);
            float tailX = offsetX + tail.x * cellSize + cellSize / 2;
            float tailY = offsetY + tail.y * cellSize + cellSize / 2;

            // Draw speed lines
            paint.setColor(Color.CYAN);
            paint.setAlpha(100);
            paint.setStrokeWidth(2);

            // Calculate direction from second-to-last to last segment
            float dirX = 0, dirY = 0;
            if (body.size() > 1) {
                Point secondLast = body.get(body.size() - 2);
                dirX = tail.x - secondLast.x;
                dirY = tail.y - secondLast.y;

                // Normalize
                float length = (float)Math.sqrt(dirX * dirX + dirY * dirY);
                if (length > 0) {
                    dirX /= length;
                    dirY /= length;
                }
            }

            // Draw multiple trail lines with enhanced effects
            Random random = new Random(System.currentTimeMillis());
            for (int i = 0; i < 8; i++) {
                float offsetAngle = (random.nextFloat() - 0.5f) * 30; // -15 to +15 degrees
                float rad = (float)Math.toRadians(offsetAngle);
                float rotDirX = (float)(dirX * Math.cos(rad) - dirY * Math.sin(rad));
                float rotDirY = (float)(dirX * Math.sin(rad) + dirY * Math.cos(rad));

                float length = cellSize * (1.5f + random.nextFloat() * 2.0f);

                // Fade out
                paint.setAlpha(100 - i * 10);

                // Draw with glow effect
                paint.setStrokeWidth(3 - i * 0.3f);
                canvas.drawLine(
                    tailX,
                    tailY,
                    tailX - rotDirX * length,
                    tailY - rotDirY * length,
                    paint
                );
            }
            paint.setAlpha(255); // Reset alpha
        }

        // Draw collision effect if needed
        if (showFlash && !body.isEmpty()) {
            Point head = body.get(0);
            float headX = offsetX + head.x * cellSize + cellSize / 2;
            float headY = offsetY + head.y * cellSize + cellSize / 2;

            float progress = moveTimer / COLLISION_FLASH_DURATION;
            GraphicsUtils.drawCollisionParticles(canvas, paint, headX, headY, cellSize * 0.4f, color, progress);
        }
    }

    /**
     * Creates a lighter version of the given color for the snake head.
     */
    private int lightenColor(int color) {
        float[] hsv = new float[3];
        Color.colorToHSV(color, hsv);

        // Increase brightness but keep the same hue
        hsv[1] *= 0.7f; // Reduce saturation a bit
        hsv[2] = Math.min(1.0f, hsv[2] * 1.3f); // Increase brightness

        return Color.HSVToColor(hsv);
    }

    /**
     * Interpolates between two colors.
     * @param color1 The first color
     * @param color2 The second color
     * @param fraction The fraction (0-1) to interpolate
     * @return The interpolated color
     */
    private int interpolateColor(int color1, int color2, float fraction) {
        float[] hsv1 = new float[3];
        float[] hsv2 = new float[3];
        Color.colorToHSV(color1, hsv1);
        Color.colorToHSV(color2, hsv2);

        // Interpolate each component
        float[] result = new float[3];
        for (int i = 0; i < 3; i++) {
            result[i] = hsv1[i] + fraction * (hsv2[i] - hsv1[i]);
        }

        return Color.HSVToColor(result);
    }

    public void addSegment() {
        if (body.isEmpty()) return;

        // Get the last segment
        Point last = body.get(body.size() - 1);

        // Add a new segment at the same position as the last one
        // It will be moved to the correct position on the next move
        body.add(new Point(last.x, last.y));
    }

    public void addSegments(int count) {
        for (int i = 0; i < count; i++) {
            addSegment();
        }
    }

    public void removeSegmentsFrom(int startIndex) {
        // Keep at least the head
        if (startIndex <= 0) startIndex = 1;

        // Remove segments
        while (body.size() > startIndex) {
            body.remove(body.size() - 1);
        }
    }

    public void die() {
        removeSegmentsFrom(1);
    }

    public void setDirection(Direction direction) {
        // Prevent 180-degree turns
        if ((this.direction == Direction.UP && direction == Direction.DOWN) ||
            (this.direction == Direction.DOWN && direction == Direction.UP) ||
            (this.direction == Direction.LEFT && direction == Direction.RIGHT) ||
            (this.direction == Direction.RIGHT && direction == Direction.LEFT)) {
            return;
        }

        this.direction = direction;
    }

    /**
     * Doubles the snake's speed (for speed power-up).
     */
    public void doubleSpeed() {
        // Store normal speed if not already stored
        if (!hasSpeedBoost) {
            normalMoveInterval = moveInterval;
        }

        // Double the speed (half the interval)
        moveInterval = normalMoveInterval / 2.0f;
        hasSpeedBoost = true;
    }

    /**
     * Resets the snake's speed to normal.
     */
    public void resetSpeed() {
        if (hasSpeedBoost) {
            moveInterval = normalMoveInterval;
            hasSpeedBoost = false;
        }
    }

    /**
     * Sets whether the snake has a speed boost.
     */
    public void setSpeedBoost(boolean hasBoost) {
        this.hasSpeedBoost = hasBoost;
    }

    public Point getHead() {
        return body.isEmpty() ? null : body.get(0);
    }

    public List<Point> getBody() {
        return body;
    }

    public int getSize() {
        return body.size();
    }

    public boolean isAI() {
        return isAI;
    }

    public int getId() {
        return id;
    }

    public int getColor() {
        return color;
    }

    public boolean hasSpeedBoost() {
        return hasSpeedBoost;
    }
}
