# Snake Clash 3D

A 3D version of the classic Snake game built with Kotlin and KorGE game engine.

## Features

- 3D snake gameplay in a 3D grid environment
- Player-controlled snake with keyboard/touch controls
- AI-controlled enemy snakes
- Food collection to grow your snake
- Score tracking
- Game over detection and restart functionality
- Dynamic camera rotation for immersive 3D experience

## Controls

- **Arrow Keys**: Control the snake's direction
- **W/A/S/D**: Adjust the camera angle
- **Touch**: Swipe in the direction you want to move

## Technical Details

- Built with <PERSON><PERSON><PERSON> for Android
- Uses KorGE game engine for 3D rendering
- Implements a 3D grid-based game system
- Features 3D models created programmatically

## How to Play

1. Control your snake (green cubes) to collect food (red cubes)
2. Avoid collisions with AI snakes (blue cubes) and yourself
3. Each food item increases your score and makes your snake longer
4. The game ends when your snake collides with an AI snake or itself
5. Press the "Restart" button to play again after game over

## Development

This game demonstrates:
- 3D rendering in KorGE
- Game state management
- Collision detection in 3D space
- Camera manipulation
- Input handling
- AI behavior programming

## Requirements

- Android device with OpenGL ES 2.0 or higher
- Android 5.0 (API level 21) or higher
