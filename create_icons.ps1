Add-Type -AssemblyName System.Drawing

# Create HDPI icon (72x72)
$bmp = New-Object System.Drawing.Bitmap(72, 72)
$graphics = [System.Drawing.Graphics]::FromImage($bmp)
$graphics.Clear([System.Drawing.Color]::Green)
$graphics.DrawString("S", (New-Object System.Drawing.Font("Arial", 48)), [System.Drawing.Brushes]::White, 10, 10)
$bmp.Save("app\src\main\res\mipmap-hdpi\ic_launcher.png", [System.Drawing.Imaging.ImageFormat]::Png)
$graphics.Dispose()
$bmp.Dispose()

# Create MDPI icon (48x48)
$bmp = New-Object System.Drawing.Bitmap(48, 48)
$graphics = [System.Drawing.Graphics]::FromImage($bmp)
$graphics.Clear([System.Drawing.Color]::Green)
$graphics.DrawString("S", (New-Object System.Drawing.Font("Arial", 32)), [System.Drawing.Brushes]::White, 6, 6)
$bmp.Save("app\src\main\res\mipmap-mdpi\ic_launcher.png", [System.Drawing.Imaging.ImageFormat]::Png)
$graphics.Dispose()
$bmp.Dispose()

# Create XHDPI icon (96x96)
$bmp = New-Object System.Drawing.Bitmap(96, 96)
$graphics = [System.Drawing.Graphics]::FromImage($bmp)
$graphics.Clear([System.Drawing.Color]::Green)
$graphics.DrawString("S", (New-Object System.Drawing.Font("Arial", 64)), [System.Drawing.Brushes]::White, 14, 14)
$bmp.Save("app\src\main\res\mipmap-xhdpi\ic_launcher.png", [System.Drawing.Imaging.ImageFormat]::Png)
$graphics.Dispose()
$bmp.Dispose()

Write-Host "Icons created successfully!"
